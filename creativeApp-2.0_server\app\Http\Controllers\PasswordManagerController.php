<?php

namespace App\Http\Controllers;

use App\Models\PasswordManager;
use App\Http\Requests\StorePasswordManagerRequest;
use App\Http\Requests\UpdatePasswordManagerRequest;
use App\Http\Resources\PasswordManagerResource;
use App\Traits\HasRoleCheck;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\QueryException;

class PasswordManagerController extends Controller
{
    use HasRoleCheck;

    public function index(Request $request): JsonResponse
    {
        try {
            $query = PasswordManager::query();

            // Eager load department, team, and user relationships
            $query->with(['department', 'team', 'user']);

            if ($request->filled('department_id')) {
                $query->where('department_id', $request->department_id);
            }

            if ($request->filled('team_id')) {
                $query->where('team_id', $request->team_id);
            }

            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            if ($request->filled('search')) {
                $query->searchByTitle($request->search);
            }

            if (!$this->userIsAdmin()) {
                $user = $this->getAuthenticatedUser();
                if ($user) {
                    $query->where('user_id', $user->id);
                }
            }

            $query->orderBy('created_at', 'desc');
            $perPage = min($request->get('per_page', 15), 100);
            $passwords = $query->paginate($perPage);

            return response()->json([
                'status' => 'success',
                'data' => PasswordManagerResource::collection($passwords->items()),
                'meta' => [
                    'current_page' => $passwords->currentPage(),
                    'per_page' => $passwords->perPage(),
                    'total' => $passwords->total(),
                    'last_page' => $passwords->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Error retrieving password records', $e);
        }
    }

    public function store(StorePasswordManagerRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $user = $this->getAuthenticatedUser();

            if (!$user) {
                return response()->json(['status' => 'error', 'message' => 'User not authenticated'], 401);
            }

            $data['user_id'] = $this->userIsAdmin() ? ($data['user_id'] ?? $user->id) : $user->id;
            $passwordManager = PasswordManager::create($data);

            return response()->json([
                'status' => 'success',
                'message' => 'Password record created successfully',
                'data' => new PasswordManagerResource($passwordManager)
            ], 201);
        } catch (QueryException $e) {
            return $this->handleQueryException($e, $data ?? []);
        } catch (\Exception $e) {
            return $this->errorResponse('Error creating password record', $e, $data ?? []);
        }
    }

    public function show(PasswordManager $passwordManager): JsonResponse
    {
        if (!$this->canAccess($passwordManager)) {
            return response()->json(['status' => 'error', 'message' => 'Unauthorized access'], 403);
        }

        return response()->json([
            'status' => 'success',
            'data' => new PasswordManagerResource($passwordManager)
        ]);
    }

    public function update(UpdatePasswordManagerRequest $request, PasswordManager $passwordManager): JsonResponse
    {
        try {
            if (!$this->canAccess($passwordManager)) {
                return response()->json(['status' => 'error', 'message' => 'Unauthorized update'], 403);
            }

            $data = $request->validated();
            $data['user_id'] = $this->userIsAdmin() ? ($data['user_id'] ?? $passwordManager->user_id) : $passwordManager->user_id;

            $passwordManager->update($data);

            return response()->json([
                'status' => 'success',
                'message' => 'Password record updated successfully',
                'data' => new PasswordManagerResource($passwordManager)
            ]);
        } catch (QueryException $e) {
            return $this->handleQueryException($e, $data ?? [], $passwordManager->id);
        } catch (\Exception $e) {
            return $this->errorResponse('Error updating password record', $e, $data ?? [], $passwordManager->id);
        }
    }

    public function destroy(PasswordManager $passwordManager): JsonResponse
    {
        if (!$this->canAccess($passwordManager)) {
            return response()->json(['status' => 'error', 'message' => 'Unauthorized delete'], 403);
        }

        $passwordManager->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Password record deleted successfully'
        ]);
    }

    public function getDecryptedPassword(PasswordManager $passwordManager): JsonResponse
    {
        if (!$this->canAccess($passwordManager)) {
            return response()->json(['status' => 'error', 'message' => 'Unauthorized'], 403);
        }

        try {
            $decrypted = $passwordManager->getDecryptedPassword();

            if ($decrypted === null) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unable to decrypt password.',
                    'password' => '',
                    'strength' => 'Unknown'
                ], 422);
            }

            return response()->json([
                'status' => 'success',
                'password' => $decrypted,
                'strength' => $passwordManager->getPasswordStrength()
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Error retrieving password', $e);
        }
    }

    // ✅ Helper Methods
    protected function canAccess(PasswordManager $passwordManager): bool
    {
        if ($this->userIsAdmin()) return true;

        $user = $this->getAuthenticatedUser();
        return $user && $passwordManager->user_id === $user->id;
    }

    protected function errorResponse(string $message, \Exception $e, array $data = [], int $id = null): JsonResponse
    {
        Log::error($message, [
            'user_id' => auth()->id(),
            'password_manager_id' => $id ?? null,
            'data' => $data,
            'error' => $e->getMessage(),
            'trace' => config('app.debug') ? $e->getTraceAsString() : 'Hidden'
        ]);

        return response()->json([
            'status' => 'error',
            'message' => $message,
            'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
        ], 500);
    }

    protected function handleQueryException(QueryException $e, array $data, int $id = null): JsonResponse
    {
        Log::error('Database error', [
            'user_id' => auth()->id(),
            'password_manager_id' => $id,
            'data' => $data,
            'error' => $e->getMessage()
        ]);

        if (str_contains($e->getMessage(), 'Duplicate entry')) {
            return response()->json([
                'status' => 'error',
                'message' => 'A password record with this title already exists for this user',
                'error' => config('app.debug') ? $e->getMessage() : 'Duplicate entry'
            ], 422);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Database error occurred',
            'error' => config('app.debug') ? $e->getMessage() : 'Database error'
        ], 500);
    }
}