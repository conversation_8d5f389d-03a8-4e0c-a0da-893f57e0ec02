import React, { useEffect, useState } from "react";
import { API_URL } from "../common/fetchData/apiConfig";
import Loading from "../common/Loading";

// Component to display a single team card
const TeamCard = ({ team }) => (
  <div className="rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow p-4">
    <div className="flex items-center justify-between">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">{team.name}</h3>
        <div className="flex items-center gap-2 mt-1">
          {team.isTopClient && <span title="Top Client" className="text-amber-500 text-sm">🔆 Priority Client</span>}
          <span className="text-xs text-gray-500">|</span>
          <span className="text-sm text-gray-600">{team.shift}</span>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {team.logo ? (
          <img
            src={team.logo}
            alt={`${team.name} logo`}
            className="w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600"
          />
        ) : (
          <div className="w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-lg font-medium text-gray-500">
            {team.name?.[0]?.toUpperCase() || "T"}
          </div>
        )}
      </div>
    </div>

    <div className="mt-4 space-y-2">
      <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2">
        <span className="material-symbols-outlined text-gray-400">person</span>
        <span>Lead:</span>
        <span className="font-medium text-gray-900 dark:text-gray-100">{team.teamLead}</span>
      </p>
      
      <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2">
        <span className="material-symbols-outlined text-gray-400">payments</span>
        <span>Status:</span>
        <span className="font-medium text-gray-900 dark:text-gray-100">{team.billingStatus}</span>
      </p>
    </div>

    <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-3">
      <div className="rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3">
        <p className="text-xs text-gray-600 dark:text-gray-300">Total Members</p>
        <div className="mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100">
          <span>👥</span>
          <span className="font-semibold">{team.totalMembers}</span>
        </div>
      </div>
      <div className="rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3">
        <p className="text-xs text-gray-600 dark:text-gray-300">Billable Hours</p>
        <div className="mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100">
          <span>⏱️</span>
          <span className="font-semibold">{team.billableHours}</span>
        </div>
      </div>
    </div>

    {team.billingRate > 0 && (
      <div className="mt-3 text-xs text-right text-gray-500">
        Rate: ${team.billingRate}/hr
      </div>
    )}
  </div>
);

// Function to normalize team data
const normalizeTeam = (item, idx) => {
  const name = item?.name || item?.team_name || item?.client_name || `Team ${idx + 1}`;
  return {
    id: item?.id ?? item?.team_id ?? item?.client_id ?? `${name}-${idx}`,
    name,
    teamLead: item?.team_lead?.name || item?.team_lead || item?.lead_name || "—",
    totalMembers: String(item?.total_members ?? item?.members_count ?? item?.member_count ?? 0).padStart(2, '0'),
    billableHours: `${item?.billable_hours ?? item?.hours ?? item?.hour ?? 0}hr`,
    logo: item?.logo_url || item?.icon || item?.team_logo || item?.client_logo || `/assets/client-logos/${name.toLowerCase().replace(/\s+/g, '-')}.png`,
    isTopClient: Boolean(item?.is_top_client ?? item?.top_client ?? item?.priority_client ?? false),
    billingStatus: item?.billing_status || "Not Set",
    shift: item?.shift || "Not Assigned",
    billingRate: item?.billing_rate || 0,
  };
};

// Main component
function ClientTeamsSection() {
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [err, setErr] = useState(null);

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (!token) {
      setErr("No auth token found");
      setLoading(false);
      return;
    }

    const load = async () => {
      setLoading(true);
      try {
        const mockTeams = [
          {
            name: "AccuWeather",
            team_lead: "Kamal Hossain",
            total_members: 10,
            billable_hours: 80,
            logo: "/assets/client-logos/accuweather.png",
            is_top_client: true
          },
          {
            name: "Bloomberg",
            team_lead: "Kamal Hossain",
            total_members: 8,
            billable_hours: 64,
            logo: "/assets/client-logos/bloomberg.png",
            is_top_client: true
          },
          {
            name: "Boats Group",
            team_lead: "Aminul Islam",
            total_members: 13,
            billable_hours: 104,
            logo: "/assets/client-logos/boats-group.png",
            is_top_client: true
          },
          {
            name: "Clipcentric",
            team_lead: "Aminul Islam",
            total_members: 15,
            billable_hours: 120,
            logo: "/assets/client-logos/clipcentric.png"
          },
          {
            name: "MultiView",
            team_lead: "Hasan Ahmed",
            total_members: 5,
            billable_hours: 40,
            logo: "/assets/client-logos/multiview.png"
          },
          {
            name: "Bigtincan",
            team_lead: "Nafiul Islam",
            total_members: 5,
            billable_hours: 40,
            logo: "/assets/client-logos/bigtincan.png"
          }
        ];

        let finalTeams = mockTeams;

        try {
          const response = await fetch(`${API_URL}/teams`, {
            headers: { 
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
          });

          if (response.ok) {
            const data = await response.json();
            if (data?.data?.length > 0 || (Array.isArray(data) && data.length > 0)) {
              finalTeams = data?.data || data;
            }
          }
        } catch (error) {
          console.warn('Failed to fetch from API, using mock data:', error);
        }

        setTeams(finalTeams.map((item, idx) => normalizeTeam(item, idx)));
      } catch (error) {
        console.error("Error fetching teams:", error);
        setErr("Unable to load teams. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    load();
  }, []);

  if (loading) return <Loading />;
  if (err) return <div className="text-red-500">{err}</div>;
  if (!teams.length) return null;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
      {teams.map((team) => (
        <TeamCard key={team.id} team={team} />
      ))}
    </div>
  );
}

// Export the component
export default ClientTeamsSection;
