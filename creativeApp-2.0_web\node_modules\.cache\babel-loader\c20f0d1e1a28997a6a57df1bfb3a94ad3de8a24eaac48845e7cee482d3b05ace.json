{"ast": null, "code": "/**\r\n * World time utilities for displaying date/time in different formats\r\n *//**\r\n * Gets current date/time strings in English, Bengali, and Hijri formats\r\n * @returns {Promise<Object>} Object containing date/time strings in different formats\r\n */export const getWorldTimeStrings=async()=>{try{const now=new Date();// --- Helper Functions ---\nconst getBengaliSeason=month=>{const seasons={'বৈশাখ':'গ্রীষ্ম','জ্যৈষ্ঠ':'গ্রীষ্ম','আষাঢ়':'বর্ষা','শ্রাবণ':'বর্ষা','ভাদ্র':'শরৎ','আশ্বিন':'শরৎ','কার্তিক':'হেমন্ত','অগ্রহায়ণ':'হেমন্ত','পৌষ':'শীত','মাঘ':'শীত','ফাল্গুন':'বসন্ত','চৈত্র':'বসন্ত'};return seasons[month]||'';};const getEnglishSeason=month=>{// Approximate seasons for the Northern Hemisphere\nif([11,0,1].includes(month))return'Winter';// Dec, Jan, Feb\nif([2,3,4].includes(month))return'Spring';// Mar, Apr, May\nif([5,6,7].includes(month))return'Summer';// Jun, Jul, Aug\nif([8,9,10].includes(month))return'Autumn';// Sep, Oct, Nov\nreturn'';};// --- English Format ---\nconst englishMonth=now.toLocaleString('en-US',{month:'long'});const englishDay=now.getDate();const englishYear=now.getFullYear();const englishWeekday=now.toLocaleString('en-US',{weekday:'long'});const englishSeason=getEnglishSeason(now.getMonth());const english=`${englishWeekday}, ${englishDay} ${englishMonth}, ${englishYear}, ${englishSeason}`;// --- Bengali Format ---\nconst bengaliFormatter=new Intl.DateTimeFormat('bn-BD',{year:'numeric',month:'long',day:'numeric',weekday:'long'});const bengaliParts=bengaliFormatter.formatToParts(now);const bengaliDateStr=bengaliParts.map(p=>p.value).join('');const bengaliMonth=bengaliParts.find(p=>p.type==='month').value;const bengaliSeason=getBengaliSeason(bengaliMonth);const bengali=`${bengaliDateStr}, ${bengaliSeason}`;// --- Hijri (Islamic) Format ---\nconst hijriFormatter=new Intl.DateTimeFormat('en-US-u-ca-islamic',{weekday:'long',year:'numeric',month:'long',day:'numeric'});const hijriDateStr=hijriFormatter.format(now);// Placeholder for Hijri season as it's complex to calculate accurately\nconst hijriSeason='Awakhirul Kharif';// Late Autumn\nconst hijri=`${hijriDateStr}, ${hijriSeason}`;return{english,bengali,hijri};}catch(error){console.error('Error getting world time strings:',error);return{english:'Error loading date/time',bengali:'তারিখ/সময় লোড করতে ব্যর্থ',hijri:'خطأ في تحميل التاريخ/الوقت'};}};/**\r\n * Gets current time in a specific timezone\r\n * @param {string} timezone - IANA timezone identifier\r\n * @returns {string} Formatted time string\r\n */export const getTimeInTimezone=timezone=>{try{const now=new Date();return new Intl.DateTimeFormat('en-US',{timeZone:timezone,hour:'2-digit',minute:'2-digit',second:'2-digit',hour12:true}).format(now);}catch(error){console.error('Error getting time in timezone:',error);return'Error loading time';}};/**\r\n * Gets current date in a specific format\r\n * @param {string} locale - Locale string (e.g., 'en-US', 'bn-BD')\r\n * @param {Object} options - Date formatting options\r\n * @returns {string} Formatted date string\r\n */export const getFormattedDate=function(){let locale=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'en-US';let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};try{const defaultOptions={weekday:'long',year:'numeric',month:'long',day:'numeric'};const mergedOptions={...defaultOptions,...options};return new Intl.DateTimeFormat(locale,mergedOptions).format(new Date());}catch(error){console.error('Error formatting date:',error);return'Error loading date';}};export default{getWorldTimeStrings,getTimeInTimezone,getFormattedDate};", "map": {"version": 3, "names": ["getWorldTimeStrings", "now", "Date", "getBengaliSeason", "month", "seasons", "getEnglishSeason", "includes", "english<PERSON>onth", "toLocaleString", "englishDay", "getDate", "englishYear", "getFullYear", "englishWeekday", "weekday", "englishSeason", "getMonth", "english", "bengaliF<PERSON>atter", "Intl", "DateTimeFormat", "year", "day", "bengaliParts", "formatToParts", "bengaliDateStr", "map", "p", "value", "join", "bengaliMonth", "find", "type", "bengaliSeason", "bengali", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hijriDateStr", "format", "hijriSeason", "hijri", "error", "console", "getTimeInTimezone", "timezone", "timeZone", "hour", "minute", "second", "hour12", "getFormattedDate", "locale", "arguments", "length", "undefined", "options", "defaultOptions", "mergedOptions"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/utils/worldTimeUtils.js"], "sourcesContent": ["/**\r\n * World time utilities for displaying date/time in different formats\r\n */\r\n\r\n/**\r\n * Gets current date/time strings in English, Bengali, and Hijri formats\r\n * @returns {Promise<Object>} Object containing date/time strings in different formats\r\n */\r\nexport const getWorldTimeStrings = async () => {\r\n  try {\r\n    const now = new Date();\r\n\r\n    // --- Helper Functions ---\r\n    const getBengaliSeason = (month) => {\r\n      const seasons = {\r\n        'বৈশাখ': 'গ্রীষ্ম',\r\n        'জ্যৈষ্ঠ': 'গ্রীষ্ম',\r\n        'আষাঢ়': 'বর্ষা',\r\n        'শ্রাবণ': 'বর্ষা',\r\n        'ভাদ্র': 'শরৎ',\r\n        'আশ্বিন': 'শরৎ',\r\n        'কার্তিক': 'হেমন্ত',\r\n        'অগ্রহায়ণ': 'হেমন্ত',\r\n        'পৌষ': 'শীত',\r\n        'মাঘ': 'শীত',\r\n        'ফাল্গুন': 'বসন্ত',\r\n        'চৈত্র': 'বসন্ত',\r\n      };\r\n      return seasons[month] || '';\r\n    };\r\n\r\n    const getEnglishSeason = (month) => {\r\n      // Approximate seasons for the Northern Hemisphere\r\n      if ([11, 0, 1].includes(month)) return 'Winter'; // Dec, Jan, Feb\r\n      if ([2, 3, 4].includes(month)) return 'Spring'; // Mar, Apr, May\r\n      if ([5, 6, 7].includes(month)) return 'Summer'; // Jun, Jul, Aug\r\n      if ([8, 9, 10].includes(month)) return 'Autumn'; // Sep, Oct, Nov\r\n      return '';\r\n    };\r\n\r\n    // --- English Format ---\r\n    const englishMonth = now.toLocaleString('en-US', { month: 'long' });\r\n    const englishDay = now.getDate();\r\n    const englishYear = now.getFullYear();\r\n    const englishWeekday = now.toLocaleString('en-US', { weekday: 'long' });\r\n    const englishSeason = getEnglishSeason(now.getMonth());\r\n    const english = `${englishWeekday}, ${englishDay} ${englishMonth}, ${englishYear}, ${englishSeason}`;\r\n\r\n    // --- Bengali Format ---\r\n    const bengaliFormatter = new Intl.DateTimeFormat('bn-BD', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric',\r\n      weekday: 'long',\r\n    });\r\n    const bengaliParts = bengaliFormatter.formatToParts(now);\r\n    const bengaliDateStr = bengaliParts.map(p => p.value).join('');\r\n    const bengaliMonth = bengaliParts.find(p => p.type === 'month').value;\r\n    const bengaliSeason = getBengaliSeason(bengaliMonth);\r\n    const bengali = `${bengaliDateStr}, ${bengaliSeason}`;\r\n\r\n    // --- Hijri (Islamic) Format ---\r\n    const hijriFormatter = new Intl.DateTimeFormat('en-US-u-ca-islamic', {\r\n      weekday: 'long',\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric',\r\n    });\r\n    const hijriDateStr = hijriFormatter.format(now);\r\n    // Placeholder for Hijri season as it's complex to calculate accurately\r\n    const hijriSeason = 'Awakhirul Kharif'; // Late Autumn\r\n    const hijri = `${hijriDateStr}, ${hijriSeason}`;\r\n\r\n    return {\r\n      english,\r\n      bengali,\r\n      hijri,\r\n    };\r\n  } catch (error) {\r\n    console.error('Error getting world time strings:', error);\r\n    return {\r\n      english: 'Error loading date/time',\r\n      bengali: 'তারিখ/সময় লোড করতে ব্যর্থ',\r\n      hijri: 'خطأ في تحميل التاريخ/الوقت'\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * Gets current time in a specific timezone\r\n * @param {string} timezone - IANA timezone identifier\r\n * @returns {string} Formatted time string\r\n */\r\nexport const getTimeInTimezone = (timezone) => {\r\n  try {\r\n    const now = new Date();\r\n    return new Intl.DateTimeFormat('en-US', {\r\n      timeZone: timezone,\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n      second: '2-digit',\r\n      hour12: true\r\n    }).format(now);\r\n  } catch (error) {\r\n    console.error('Error getting time in timezone:', error);\r\n    return 'Error loading time';\r\n  }\r\n};\r\n\r\n/**\r\n * Gets current date in a specific format\r\n * @param {string} locale - Locale string (e.g., 'en-US', 'bn-BD')\r\n * @param {Object} options - Date formatting options\r\n * @returns {string} Formatted date string\r\n */\r\nexport const getFormattedDate = (locale = 'en-US', options = {}) => {\r\n  try {\r\n    const defaultOptions = {\r\n      weekday: 'long',\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    };\r\n    \r\n    const mergedOptions = { ...defaultOptions, ...options };\r\n    return new Intl.DateTimeFormat(locale, mergedOptions).format(new Date());\r\n  } catch (error) {\r\n    console.error('Error formatting date:', error);\r\n    return 'Error loading date';\r\n  }\r\n};\r\n\r\nexport default {\r\n  getWorldTimeStrings,\r\n  getTimeInTimezone,\r\n  getFormattedDate\r\n};\r\n\r\n\r\n"], "mappings": "AAAA;AACA;AACA,GAEA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAA,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CAC7C,GAAI,CACF,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAEtB;AACA,KAAM,CAAAC,gBAAgB,CAAIC,KAAK,EAAK,CAClC,KAAM,CAAAC,OAAO,CAAG,CACd,OAAO,CAAE,SAAS,CAClB,SAAS,CAAE,SAAS,CACpB,OAAO,CAAE,OAAO,CAChB,QAAQ,CAAE,OAAO,CACjB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,QAAQ,CACnB,WAAW,CAAE,QAAQ,CACrB,KAAK,CAAE,KAAK,CACZ,KAAK,CAAE,KAAK,CACZ,SAAS,CAAE,OAAO,CAClB,OAAO,CAAE,OACX,CAAC,CACD,MAAO,CAAAA,OAAO,CAACD,KAAK,CAAC,EAAI,EAAE,CAC7B,CAAC,CAED,KAAM,CAAAE,gBAAgB,CAAIF,KAAK,EAAK,CAClC;AACA,GAAI,CAAC,EAAE,CAAE,CAAC,CAAE,CAAC,CAAC,CAACG,QAAQ,CAACH,KAAK,CAAC,CAAE,MAAO,QAAQ,CAAE;AACjD,GAAI,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACG,QAAQ,CAACH,KAAK,CAAC,CAAE,MAAO,QAAQ,CAAE;AAChD,GAAI,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACG,QAAQ,CAACH,KAAK,CAAC,CAAE,MAAO,QAAQ,CAAE;AAChD,GAAI,CAAC,CAAC,CAAE,CAAC,CAAE,EAAE,CAAC,CAACG,QAAQ,CAACH,KAAK,CAAC,CAAE,MAAO,QAAQ,CAAE;AACjD,MAAO,EAAE,CACX,CAAC,CAED;AACA,KAAM,CAAAI,YAAY,CAAGP,GAAG,CAACQ,cAAc,CAAC,OAAO,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAC,CAAC,CACnE,KAAM,CAAAM,UAAU,CAAGT,GAAG,CAACU,OAAO,CAAC,CAAC,CAChC,KAAM,CAAAC,WAAW,CAAGX,GAAG,CAACY,WAAW,CAAC,CAAC,CACrC,KAAM,CAAAC,cAAc,CAAGb,GAAG,CAACQ,cAAc,CAAC,OAAO,CAAE,CAAEM,OAAO,CAAE,MAAO,CAAC,CAAC,CACvE,KAAM,CAAAC,aAAa,CAAGV,gBAAgB,CAACL,GAAG,CAACgB,QAAQ,CAAC,CAAC,CAAC,CACtD,KAAM,CAAAC,OAAO,CAAG,GAAGJ,cAAc,KAAKJ,UAAU,IAAIF,YAAY,KAAKI,WAAW,KAAKI,aAAa,EAAE,CAEpG;AACA,KAAM,CAAAG,gBAAgB,CAAG,GAAI,CAAAC,IAAI,CAACC,cAAc,CAAC,OAAO,CAAE,CACxDC,IAAI,CAAE,SAAS,CACflB,KAAK,CAAE,MAAM,CACbmB,GAAG,CAAE,SAAS,CACdR,OAAO,CAAE,MACX,CAAC,CAAC,CACF,KAAM,CAAAS,YAAY,CAAGL,gBAAgB,CAACM,aAAa,CAACxB,GAAG,CAAC,CACxD,KAAM,CAAAyB,cAAc,CAAGF,YAAY,CAACG,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAC9D,KAAM,CAAAC,YAAY,CAAGP,YAAY,CAACQ,IAAI,CAACJ,CAAC,EAAIA,CAAC,CAACK,IAAI,GAAK,OAAO,CAAC,CAACJ,KAAK,CACrE,KAAM,CAAAK,aAAa,CAAG/B,gBAAgB,CAAC4B,YAAY,CAAC,CACpD,KAAM,CAAAI,OAAO,CAAG,GAAGT,cAAc,KAAKQ,aAAa,EAAE,CAErD;AACA,KAAM,CAAAE,cAAc,CAAG,GAAI,CAAAhB,IAAI,CAACC,cAAc,CAAC,oBAAoB,CAAE,CACnEN,OAAO,CAAE,MAAM,CACfO,IAAI,CAAE,SAAS,CACflB,KAAK,CAAE,MAAM,CACbmB,GAAG,CAAE,SACP,CAAC,CAAC,CACF,KAAM,CAAAc,YAAY,CAAGD,cAAc,CAACE,MAAM,CAACrC,GAAG,CAAC,CAC/C;AACA,KAAM,CAAAsC,WAAW,CAAG,kBAAkB,CAAE;AACxC,KAAM,CAAAC,KAAK,CAAG,GAAGH,YAAY,KAAKE,WAAW,EAAE,CAE/C,MAAO,CACLrB,OAAO,CACPiB,OAAO,CACPK,KACF,CAAC,CACH,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzD,MAAO,CACLvB,OAAO,CAAE,yBAAyB,CAClCiB,OAAO,CAAE,4BAA4B,CACrCK,KAAK,CAAE,4BACT,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAG,iBAAiB,CAAIC,QAAQ,EAAK,CAC7C,GAAI,CACF,KAAM,CAAA3C,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,MAAO,IAAI,CAAAkB,IAAI,CAACC,cAAc,CAAC,OAAO,CAAE,CACtCwB,QAAQ,CAAED,QAAQ,CAClBE,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,IACV,CAAC,CAAC,CAACX,MAAM,CAACrC,GAAG,CAAC,CAChB,CAAE,MAAOwC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvD,MAAO,oBAAoB,CAC7B,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAS,gBAAgB,CAAG,QAAAA,CAAA,CAAoC,IAAnC,CAAAC,MAAM,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,OAAO,IAAE,CAAAG,OAAO,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC7D,GAAI,CACF,KAAM,CAAAI,cAAc,CAAG,CACrBzC,OAAO,CAAE,MAAM,CACfO,IAAI,CAAE,SAAS,CACflB,KAAK,CAAE,MAAM,CACbmB,GAAG,CAAE,SACP,CAAC,CAED,KAAM,CAAAkC,aAAa,CAAG,CAAE,GAAGD,cAAc,CAAE,GAAGD,OAAQ,CAAC,CACvD,MAAO,IAAI,CAAAnC,IAAI,CAACC,cAAc,CAAC8B,MAAM,CAAEM,aAAa,CAAC,CAACnB,MAAM,CAAC,GAAI,CAAApC,IAAI,CAAC,CAAC,CAAC,CAC1E,CAAE,MAAOuC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,MAAO,oBAAoB,CAC7B,CACF,CAAC,CAED,cAAe,CACbzC,mBAAmB,CACnB2C,iBAAiB,CACjBO,gBACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}