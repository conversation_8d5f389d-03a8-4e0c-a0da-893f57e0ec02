import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AddTeam = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [users, setUsers] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [teamName, setTeamName] = useState('');
    const [icon, setIcon] = useState(null);
    const [logo, setLogo] = useState(null);
    const [poc, setPoc] = useState('');
    const [manager, setManager] = useState('');
    const [teamLead, setTeamLead] = useState('');
    const [launch, setLaunch] = useState('');
    const [workday, setWorkday] = useState([]);
    const [departmentId, setDepartmentId] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);
    const [loading, setLoading] = useState(true);

    // Days of the week for multi-select
    const daysOfWeek = [
        'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
    ];

    // Handle workday selection
    const handleWorkdayChange = (day) => {
        setWorkday(prev => {
            if (prev.includes(day)) {
                return prev.filter(d => d !== day);
            } else {
                return [...prev, day];
            }
        });
    };

    useEffect(() => {
        const fetchData = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                // Fetch Users
                const usersResponse = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!usersResponse.ok) {
                    throw new Error('Failed to fetch users');
                }

                const usersData = await usersResponse.json();
                setUsers(usersData.map(user => ({
                    id: user.id,
                    fullName: `${user.fname || ''} ${user.lname || ''}`.trim(),
                })));

                // Fetch Departments
                const departmentsResponse = await fetch(`${API_URL}/departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!departmentsResponse.ok) {
                    throw new Error('Failed to fetch departments');
                }

                const departmentsData = await departmentsResponse.json();
                setDepartments(departmentsData.departments); // Assuming the response has 'departments' array
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault(); // Prevent default form submission behavior

        // Get user_id from localStorage for 'created_by'
        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }
    
        const trimmedTeamName = teamName.trim();
    
        // Check if the team already exists
        const teamExists = teams.some((team) => {
            const teamNameLower = team.name.toLowerCase().trim();
            return teamNameLower === trimmedTeamName.toLowerCase();
        });
    
        if (teamExists) {
            setError('Team already exists. Please add a different team.');
            setTimeout(() => setError(''), 3000);
            return; // Exit if the team already exists
        }
    
        setError(''); // Clear any previous error
    
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return; // Exit if token is not available
            }
    
            const formData = new FormData();
            formData.append('name', trimmedTeamName);
            formData.append('icon', icon);
            formData.append('logo', logo);
            formData.append('poc', poc);
            formData.append('manager', manager);
            formData.append('team_lead', teamLead);
            formData.append('workday', workday.join(', '));
            formData.append('launch', launch);
            formData.append('department_id', departmentId);
            formData.append('created_by', createdBy);

    
            const response = await fetch(`${API_URL}/teams`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
                body: formData,
            });
    
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to save team: ' + response.statusText);
            }
    
            const result = await response.json();
            //setSuccessMessage(`Team "${result.name || trimmedTeamName}" added successfully!`);
            alertMessage('success');

            setTeamName('');
            setIcon(null);
            setLogo(null);
            setPoc('');
            setManager('');
            setTeamLead('');
            setLaunch('');
            setWorkday([]);
            setDepartmentId('');
    
            // Refetch the teams list
            const newTeamsResponse = await fetch(`${API_URL}/teams`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!newTeamsResponse.ok) {
                throw new Error('Failed to fetch teams: ' + newTeamsResponse.statusText);
            }
    
            const newTeamsData = await newTeamsResponse.json();
            setTeams(newTeamsData.teams); // Update the teams list
        } catch (error) {
            setError(error.message || 'Failed to add team.');
            console.error('Error adding team:', error);
        }
    };
    

    if (!isVisible) return null;

    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                <div className="bg-white rounded-lg shadow-md w-full max-w-3xl relative overflow-y-auto h-[80vh] mt-10 scrollbar-vertical">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                        <h4 className="text-xl text-left font-medium text-gray-800">Add New Team</h4>
                        <button onClick={() => setVisible(false)}
                            className="text-3xl text-gray-500 hover:text-gray-800"
                            
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className="text-left p-6">
                        <div className=''>                           
                            <div className="mb-4">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                    Department
                                </label>
                                <select
                                    id="department"
                                    value={departmentId}
                                    onChange={(e) => setDepartmentId(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select Department</option>
                                    {departments.map((department) => (
                                        <option key={department.id} value={department.id}>
                                            {department.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="mb-4">
                                <label htmlFor="teamName" className="block text-sm font-medium text-gray-700 pb-4">
                                    Team Name
                                </label>
                                <input
                                    type="text"
                                    id="teamName"
                                    value={teamName}
                                    onChange={(e) => {
                                        setTeamName(e.target.value);
                                        if (error) setError(''); // Clear error when user starts typing
                                    }}
                                    required
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                                {error && <p className="text-red-500 text-sm">{error}</p>}
                            </div>

                            <div className="mb-4">
                                <label htmlFor="icon" className="block text-sm font-medium text-gray-700 pb-4">
                                    Icon
                                </label>
                                <input
                                    type="file"
                                    id="icon"
                                    onChange={(e) => setIcon(e.target.files[0])}
                                    accept="image/*"
                                    required
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            <div className="mb-4">
                                <label htmlFor="logo" className="block text-sm font-medium text-gray-700 pb-4">
                                    Logo
                                </label>
                                <input
                                    type="file"
                                    id="logo"
                                    onChange={(e) => setLogo(e.target.files[0])}
                                    accept="image/*"
                                    required
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>

                            <div className="mb-4">
                                <label htmlFor="poc" className="block text-sm font-medium text-gray-700 pb-4">
                                    Point of Contact
                                </label>
                                <select
                                    id="poc"
                                    value={poc}
                                    onChange={(e) => setPoc(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select POC</option>
                                    {users.map((user) => user.fullName && (
                                        <option key={user.id} value={user.fullName}>
                                            {user.fullName}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="manager" className="block text-sm font-medium text-gray-700 pb-4">
                                    Manager
                                </label>
                                <select
                                    id="manager"
                                    value={manager}
                                    onChange={(e) => setManager(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select Manager</option>
                                    {users.map((user) => user.fullName && (
                                        <option key={user.id} value={user.fullName}>
                                            {user.fullName}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="teamLead" className="block text-sm font-medium text-gray-700 pb-4">
                                    Team Lead
                                </label>
                                <select
                                    id="teamLead"
                                    value={teamLead}
                                    onChange={(e) => setTeamLead(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select Team Lead</option>
                                    {users.map((user) => user.fullName && (
                                        <option key={user.id} value={user.fullName}>
                                            {user.fullName}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 pb-4">
                                    Work Days
                                </label>
                                <div className="grid grid-cols-2 gap-2">
                                    {daysOfWeek.map((day) => (
                                        <label key={day} className="flex items-center space-x-2 cursor-pointer">
                                            <input
                                                type="checkbox"
                                                checked={workday.includes(day)}
                                                onChange={() => handleWorkdayChange(day)}
                                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            />
                                            <span className="text-sm text-gray-700">{day}</span>
                                        </label>
                                    ))}
                                </div>
                                {workday.length > 0 && (
                                    <p className="text-xs text-gray-500 mt-2">
                                        Selected: {workday.join(', ')}
                                    </p>
                                )}
                            </div>

                            <div className="mb-4">
                                <label htmlFor="launch" className="block text-sm font-medium text-gray-700 pb-4">
                                    Launch Date
                                </label>
                                <input
                                    type="date"
                                    id="launch"
                                    value={launch}
                                    onChange={(e) => setLaunch(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>
                        </div>

                        {error && <p className="text-red-500 text-sm">{error}</p>}
                        
                        <div className='text-left pt-6'>
                            <button
                                type="submit"
                                className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                            >
                                <span class="material-symbols-rounded text-white text-xl font-regular">add_circle</span>
                                {loading ? 'Adding...' : 'Add Team'}
                            </button>
                        </div>

                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
          
        </>
    );
};

export default AddTeam;
