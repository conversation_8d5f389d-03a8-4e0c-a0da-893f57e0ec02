{"ast": null, "code": "import{useState,useEffect}from'react';const isTokenValid=()=>{const token=localStorage.getItem('token');return token&&token.length>0;};export const useTeamData=()=>{const[teamLead,setTeamLead]=useState('');const[totalMembers,setTotalMembers]=useState(0);const[billableHours,setBillableHours]=useState(0);useEffect(()=>{const fetchTeamData=async()=>{if(!isTokenValid())return;try{const token=localStorage.getItem('token');const response=await fetch(`${process.env.REACT_APP_BASE_API_URL}team/summary`,{headers:{'Authorization':`Bearer ${token}`}});if(!response.ok)throw new Error('Failed to fetch team data');const data=await response.json();setTeamLead(data.teamLead||'N/A');setTotalMembers(data.totalMembers||0);setBillableHours(data.billableHours||0);}catch(error){console.error('Error fetching team data:',error);}};fetchTeamData();},[]);return{teamLead,totalMembers,billableHours};};", "map": {"version": 3, "names": ["useState", "useEffect", "isTokenValid", "token", "localStorage", "getItem", "length", "useTeamData", "teamLead", "setTeamLead", "totalMembers", "setTotalMembers", "billableHours", "setBillableHours", "fetchTeamData", "response", "fetch", "process", "env", "REACT_APP_BASE_API_URL", "headers", "ok", "Error", "data", "json", "error", "console"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/hooks/useTeamData.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nconst isTokenValid = () => {\r\n  const token = localStorage.getItem('token');\r\n  return token && token.length > 0;\r\n};\r\n\r\nexport const useTeamData = () => {\r\n  const [teamLead, setTeamLead] = useState('');\r\n  const [totalMembers, setTotalMembers] = useState(0);\r\n  const [billableHours, setBillableHours] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const fetchTeamData = async () => {\r\n      if (!isTokenValid()) return;\r\n      \r\n      try {\r\n        const token = localStorage.getItem('token');\r\n        const response = await fetch(\r\n          `${process.env.REACT_APP_BASE_API_URL}team/summary`,\r\n          {\r\n            headers: {\r\n              'Authorization': `Bearer ${token}`\r\n            }\r\n          }\r\n        );\r\n        \r\n        if (!response.ok) throw new Error('Failed to fetch team data');\r\n        \r\n        const data = await response.json();\r\n        setTeamLead(data.teamLead || 'N/A');\r\n        setTotalMembers(data.totalMembers || 0);\r\n        setBillableHours(data.billableHours || 0);\r\n      } catch (error) {\r\n        console.error('Error fetching team data:', error);\r\n      }\r\n    };\r\n\r\n    fetchTeamData();\r\n  }, []);\r\n\r\n  return { teamLead, totalMembers, billableHours };\r\n};"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,MAAO,CAAAF,KAAK,EAAIA,KAAK,CAACG,MAAM,CAAG,CAAC,CAClC,CAAC,CAED,MAAO,MAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGT,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACU,YAAY,CAAEC,eAAe,CAAC,CAAGX,QAAQ,CAAC,CAAC,CAAC,CACnD,KAAM,CAACY,aAAa,CAAEC,gBAAgB,CAAC,CAAGb,QAAQ,CAAC,CAAC,CAAC,CAErDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAa,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CAACZ,YAAY,CAAC,CAAC,CAAE,OAErB,GAAI,CACF,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,KAAM,CAAAU,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAC1B,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,cAAc,CACnD,CACEC,OAAO,CAAE,CACP,eAAe,CAAE,UAAUjB,KAAK,EAClC,CACF,CACF,CAAC,CAED,GAAI,CAACY,QAAQ,CAACM,EAAE,CAAE,KAAM,IAAI,CAAAC,KAAK,CAAC,2BAA2B,CAAC,CAE9D,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAR,QAAQ,CAACS,IAAI,CAAC,CAAC,CAClCf,WAAW,CAACc,IAAI,CAACf,QAAQ,EAAI,KAAK,CAAC,CACnCG,eAAe,CAACY,IAAI,CAACb,YAAY,EAAI,CAAC,CAAC,CACvCG,gBAAgB,CAACU,IAAI,CAACX,aAAa,EAAI,CAAC,CAAC,CAC3C,CAAE,MAAOa,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CAAC,CAEDX,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,MAAO,CAAEN,QAAQ,CAAEE,YAAY,CAAEE,aAAc,CAAC,CAClD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}