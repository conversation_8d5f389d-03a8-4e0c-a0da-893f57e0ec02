/**
 * World time utilities for displaying date/time in different formats
 */

/**
 * Gets current date/time strings in English, Bengali, and Hijri formats
 * @returns {Promise<Object>} Object containing date/time strings in different formats
 */
export const getWorldTimeStrings = async () => {
  try {
    const now = new Date();

    // --- Helper Functions ---
    const getBengaliSeason = (month) => {
      const seasons = {
        'বৈশাখ': 'গ্রীষ্ম',
        'জ্যৈষ্ঠ': 'গ্রীষ্ম',
        'আষাঢ়': 'বর্ষা',
        'শ্রাবণ': 'বর্ষা',
        'ভাদ্র': 'শরৎ',
        'আশ্বিন': 'শরৎ',
        'কার্তিক': 'হেমন্ত',
        'অগ্রহায়ণ': 'হেমন্ত',
        'পৌষ': 'শীত',
        'মাঘ': 'শীত',
        'ফাল্গুন': 'বসন্ত',
        'চৈত্র': 'বসন্ত',
      };
      return seasons[month] || '';
    };

    const getEnglishSeason = (month) => {
      // Approximate seasons for the Northern Hemisphere
      if ([11, 0, 1].includes(month)) return 'Winter'; // Dec, Jan, Feb
      if ([2, 3, 4].includes(month)) return 'Spring'; // Mar, Apr, May
      if ([5, 6, 7].includes(month)) return 'Summer'; // Jun, Jul, Aug
      if ([8, 9, 10].includes(month)) return 'Autumn'; // Sep, Oct, Nov
      return '';
    };

    // --- English Format ---
    const englishMonth = now.toLocaleString('en-US', { month: 'long' });
    const englishDay = now.getDate();
    const englishYear = now.getFullYear();
    const englishWeekday = now.toLocaleString('en-US', { weekday: 'long' });
    const englishSeason = getEnglishSeason(now.getMonth());
    const english = `${englishWeekday}, ${englishDay} ${englishMonth}, ${englishYear}, ${englishSeason}`;

    // --- Bengali Format ---
    const bengaliFormatter = new Intl.DateTimeFormat('bn-BD', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long',
    });
    const bengaliParts = bengaliFormatter.formatToParts(now);
    const bengaliDateStr = bengaliParts.map(p => p.value).join('');
    const bengaliMonth = bengaliParts.find(p => p.type === 'month').value;
    const bengaliSeason = getBengaliSeason(bengaliMonth);
    const bengali = `${bengaliDateStr}, ${bengaliSeason}`;

    // --- Hijri (Islamic) Format ---
    const hijriFormatter = new Intl.DateTimeFormat('en-US-u-ca-islamic', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    const hijriDateStr = hijriFormatter.format(now);
    // Placeholder for Hijri season as it's complex to calculate accurately
    const hijriSeason = 'Awakhirul Kharif'; // Late Autumn
    const hijri = `${hijriDateStr}, ${hijriSeason}`;

    return {
      english,
      bengali,
      hijri,
    };
  } catch (error) {
    console.error('Error getting world time strings:', error);
    return {
      english: 'Error loading date/time',
      bengali: 'তারিখ/সময় লোড করতে ব্যর্থ',
      hijri: 'خطأ في تحميل التاريخ/الوقت'
    };
  }
};

/**
 * Gets current time in a specific timezone
 * @param {string} timezone - IANA timezone identifier
 * @returns {string} Formatted time string
 */
export const getTimeInTimezone = (timezone) => {
  try {
    const now = new Date();
    return new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    }).format(now);
  } catch (error) {
    console.error('Error getting time in timezone:', error);
    return 'Error loading time';
  }
};

/**
 * Gets current date in a specific format
 * @param {string} locale - Locale string (e.g., 'en-US', 'bn-BD')
 * @param {Object} options - Date formatting options
 * @returns {string} Formatted date string
 */
export const getFormattedDate = (locale = 'en-US', options = {}) => {
  try {
    const defaultOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    
    const mergedOptions = { ...defaultOptions, ...options };
    return new Intl.DateTimeFormat(locale, mergedOptions).format(new Date());
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Error loading date';
  }
};

export default {
  getWorldTimeStrings,
  getTimeInTimezone,
  getFormattedDate
};


