{"__meta": {"id": "X7322e13aae7f039af19bb6b06a50f40a", "datetime": "2025-08-11 17:58:53", "utime": **********.515383, "method": "POST", "uri": "/api/teams", "ip": "127.0.0.1"}, "php": {"version": "8.0.30", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[17:58:53] LOG.info: Authenticated user roles: [\n    \"super-admin\"\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.247222, "xdebug_link": null, "collector": "log"}, {"message": "[17:58:53] LOG.info: Authenticated User: {\n    \"user_id\": 150,\n    \"fname\": null,\n    \"lname\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.248968, "xdebug_link": null, "collector": "log"}, {"message": "[17:58:53] LOG.info: Create Team Request: {\n    \"request\": {\n        \"name\": \"<PERSON><PERSON><PERSON>\",\n        \"poc\": \"Shafayatul Islam\",\n        \"manager\": \"Shafayatul Islam\",\n        \"team_lead\": \"<PERSON><PERSON><PERSON> Islam\",\n        \"workday\": \"[\\\"Monday\\\",\\\"Tuesday\\\"]\",\n        \"launch\": \"2025-08-18\",\n        \"department_id\": \"7\",\n        \"created_by\": \"150\",\n        \"icon\": {},\n        \"logo\": {}\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.26254, "xdebug_link": null, "collector": "log"}, {"message": "[17:58:53] LOG.error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'workday' in 'field list' (SQL: insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (Clipcentric, images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png, images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, \"[\\\"Monday\\\",\\\"Tuesday\\\"]\", 2025-08-18, 150, 2025-08-11 17:58:53, 2025-08-11 17:58:53)) {\n    \"userId\": 150,\n    \"exception\": {\n        \"errorInfo\": [\n            \"42S22\",\n            1054,\n            \"Unknown column 'workday' in 'field list'\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.27173, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754913532.834452, "end": **********.5154, "duration": 0.68094801902771, "duration_str": "681ms", "measures": [{"label": "Booting", "start": 1754913532.834452, "relative_start": 0, "end": **********.193363, "relative_end": **********.193363, "duration": 0.35891103744506836, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.19337, "relative_start": 0.3589181900024414, "end": **********.515401, "relative_end": 9.5367431640625e-07, "duration": 0.32203078269958496, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30598024, "peak_usage_str": "29MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\Database\\QueryException", "message": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'workday' in 'field list' (SQL: insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (Clipcentric, images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png, images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, \"[\\\"Monday\\\",\\\"Tuesday\\\"]\", 2025-08-18, 150, 2025-08-11 17:58:53, 2025-08-11 17:58:53))", "code": "42S22", "file": "vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 760, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-478711771 data-indent-pad=\"  \"><span class=sf-dump-note>array:70</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>720</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">runQueryCallback</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>546</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>498</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">statement</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"79 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">insert</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>3322</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">processInsertGetId</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Illuminate\\Database\\Query\\Processors\\Processor</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Database\\Query\\Builder]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1869</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">insertGetId</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Database\\Query\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1330</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">insertGetId</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n          \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n          \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n          \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n          \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n          \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1295</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">insertAndSetId</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">[object Illuminate\\Database\\Eloquent\\Builder]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1138</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">performInsert</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">[object Illuminate\\Database\\Eloquent\\Builder]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>986</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">vendor/laravel/framework/src/Illuminate/Support/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>319</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\Database\\Eloquent\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">[object App\\Models\\Team]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>987</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">tap</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">[object App\\Models\\Team]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n        \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2330</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">forwardCallTo</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">[object Illuminate\\Database\\Eloquent\\Builder]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n          \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n          \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n          \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n          \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n          \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2342</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n          \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n          \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n          \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n          \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n          \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"39 characters\">app/Http/Controllers/TeamController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>272</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">__callStatic</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n          \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n          \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n          \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n          \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n          \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Routing/Controller.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">createTeam</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Http\\Controllers\\TeamController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callAction</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Routing\\Controller</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">createTeam</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref>#46</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">json</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">convertedFiles</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#1751</a><samp data-depth=7 class=sf-dump-compact>\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"20 characters\">accuweather-logo.png</span>\"\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: \"<span class=sf-dump-str title=\"40 characters\">LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG</span>\"\n              <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"12 characters\">C:\\xampp\\tmp</span>\"\n              <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D28.tmp</span>\"\n              <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D28.tmp</span>\"\n              <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D28.tmp</span>\"\n              <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n              <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\tmp\\php5D28.tmp\n24 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>tmp\\php5D28.tmp</span>\"\n              <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"**********\">2025-08-11 17:58:53</span>\n              <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n              <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n              <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>4503599627795477</span>\n              <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>6000</span>\n              <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n              <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n              <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n              <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n              <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D28.tmp</span>\"\n            </samp>}\n            \"<span class=sf-dump-key>logo</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#1750</a><samp data-depth=7 class=sf-dump-compact>\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"10 characters\">avatar.png</span>\"\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: \"<span class=sf-dump-str title=\"40 characters\">YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq</span>\"\n              <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"12 characters\">C:\\xampp\\tmp</span>\"\n              <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D29.tmp</span>\"\n              <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D29.tmp</span>\"\n              <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D29.tmp</span>\"\n              <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n              <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\tmp\\php5D29.tmp\n24 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>tmp\\php5D29.tmp</span>\"\n              <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"**********\">2025-08-11 17:58:53</span>\n              <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n              <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n              <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>1970324837399574</span>\n              <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>29876</span>\n              <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n              <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n              <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n              <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n              <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D29.tmp</span>\"\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">userResolver</span>: <span class=sf-dump-note>Closure($guard = null)</span> {<a class=sf-dump-ref>#1628</a><samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Auth\\AuthServiceProvider\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>AuthServiceProvider</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Auth\\AuthServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>AuthServiceProvider</span> {<a class=sf-dump-ref>#171</a> &#8230;}\n            <span class=sf-dump-meta>use</span>: {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>$app</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref>#2</a> &#8230;}\n            </samp>}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php\n119 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Auth\\AuthServiceProvider.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">88 to 90</span>\"\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">routeResolver</span>: <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#1638</a><samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Router\n25 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Router</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router</span> {<a class=sf-dump-ref>#27</a> &#8230;}\n            <span class=sf-dump-meta>use</span>: {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>$route</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref>#271</a> &#8230;}\n            </samp>}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php\n109 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">771 to 771</span>\"\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ParameterBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ParameterBag</span> {<a class=sf-dump-ref>#48</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#47</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n              \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n              \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n              \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n              \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n              \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n              \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n              \"<span class=sf-dump-key>created_by</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">query</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#54</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">server</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ServerBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ServerBag</span> {<a class=sf-dump-ref>#51</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:34</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"57 characters\">C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\public</span>\"\n              \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n              \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54468</span>\"\n              \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.0.30 Development Server</span>\"\n              \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n              \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n              \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n              \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/api/teams</span>\"\n              \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n              \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n              \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"67 characters\">C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\public\\index.php</span>\"\n              \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/api/teams</span>\"\n              \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/index.php/api/teams</span>\"\n              \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n              \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n              \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">37054</span>\"\n              \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">37054</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n              \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Bearer 433|PNWjra2rBuzPADQf2tOmLxHHnAJUULltoppLJabQ6b2369d9</span>\"\n              \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n              \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryE1TEeURarnw7XoDk</span>\"\n              \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryE1TEeURarnw7XoDk</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n              \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n              \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n              \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754913532.8345</span>\n              \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754913532</span>\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">files</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\FileBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileBag</span> {<a class=sf-dump-ref>#50</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\File\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation\\File</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#34</a><samp data-depth=8 class=sf-dump-compact>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"20 characters\">accuweather-logo.png</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"12 characters\">C:\\xampp\\tmp</span>\"\n                <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D28.tmp</span>\"\n                <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D28.tmp</span>\"\n                <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D28.tmp</span>\"\n                <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n                <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\tmp\\php5D28.tmp\n24 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>tmp\\php5D28.tmp</span>\"\n                <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"**********\">2025-08-11 17:58:53</span>\n                <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n                <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n                <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>4503599627795477</span>\n                <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>6000</span>\n                <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n                <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n                <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D28.tmp</span>\"\n              </samp>}\n              \"<span class=sf-dump-key>logo</span>\" => <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\File\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation\\File</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#35</a><samp data-depth=8 class=sf-dump-compact>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"10 characters\">avatar.png</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"12 characters\">C:\\xampp\\tmp</span>\"\n                <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D29.tmp</span>\"\n                <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D29.tmp</span>\"\n                <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D29.tmp</span>\"\n                <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n                <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\tmp\\php5D29.tmp\n24 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>tmp\\php5D29.tmp</span>\"\n                <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"**********\">2025-08-11 17:58:53</span>\n                <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n                <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n                <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>1970324837399574</span>\n                <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>29876</span>\n                <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n                <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n                <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D29.tmp</span>\"\n              </samp>}\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">cookies</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#49</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\HeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HeaderBag</span> {<a class=sf-dump-ref>#52</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:17</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">37054</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">Bearer 433|PNWjra2rBuzPADQf2tOmLxHHnAJUULltoppLJabQ6b2369d9</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryE1TEeURarnw7XoDk</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">content</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">languages</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">charsets</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">encodings</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">acceptableContentTypes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">pathInfo</span>: \"<span class=sf-dump-str title=\"10 characters\">/api/teams</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">requestUri</span>: \"<span class=sf-dump-str title=\"10 characters\">/api/teams</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">baseUrl</span>: \"\"\n          #<span class=sf-dump-protected title=\"Protected property\">basePath</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">method</span>: \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">format</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">locale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">preferredFormat</span>: <span class=sf-dump-const>null</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isHostValid</span>: <span class=sf-dump-const>true</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isForwardedValid</span>: <span class=sf-dump-const>true</span>\n          <span class=sf-dump-meta>basePath</span>: \"\"\n          <span class=sf-dump-meta>format</span>: \"<span class=sf-dump-str title=\"4 characters\">html</span>\"\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>260</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"44 characters\">[object App\\Http\\Controllers\\TeamController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">createTeam</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>205</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>798</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">app/Http/Middleware/RoleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\RoleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"17 characters\">super-admin|admin</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>30</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"28 characters\">app/Http/Middleware/Cors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Http\\Middleware\\Cors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"79 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"79 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Illuminate\\Routing\\Middleware\\ThrottleRequests</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => {<a class=sf-dump-ref>#1687</a><samp data-depth=5 class=sf-dump-compact>\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">key</span>\": \"<span class=sf-dump-str title=\"40 characters\">13682ac418603aa0966369d46bbf282f562acf47</span>\"\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">maxAttempts</span>\": <span class=sf-dump-num>150</span>\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">decayMinutes</span>\": \"<span class=sf-dump-str>1</span>\"\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">responseCallback</span>\": <span class=sf-dump-const>null</span>\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Illuminate\\Routing\\Middleware\\ThrottleRequests</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>1</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>44</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">sanctum</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>25</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Laravel\\Sanctum\\Http\\Middleware\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>799</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>776</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>740</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>729</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"28 characters\">app/Http/Middleware/Cors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Http\\Middleware\\Cors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>52</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"67 characters\">C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-478711771\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["        // message to include the bindings with SQL, which will make this exception a\n", "        // lot more helpful to the developer instead of just the database's errors.\n", "        catch (Exception $e) {\n", "            throw new QueryException(\n", "                $query, $this->prepareBindings($bindings), $e\n", "            );\n", "        }\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FConnection.php&line=760", "ajax": false, "filename": "Connection.php", "line": "760"}}, {"type": "PDOException", "message": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'workday' in 'field list'", "code": "42S22", "file": "vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 539, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1425201359 data-indent-pad=\"  \"><span class=sf-dump-note>array:72</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>539</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">prepare</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PDO</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>753</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Database\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>720</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">runQueryCallback</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>546</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Database/Connection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>498</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">statement</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"79 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">insert</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Database\\Connection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>3322</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">processInsertGetId</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Illuminate\\Database\\Query\\Processors\\Processor</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Database\\Query\\Builder]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"179 characters\">insert into `teams` (`name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `workday`, `launch`, `created_by`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1869</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">insertGetId</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Database\\Query\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-num>150</span>\n        <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1330</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">insertGetId</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n          \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n          \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n          \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n          \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n          \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1295</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">insertAndSetId</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">[object Illuminate\\Database\\Eloquent\\Builder]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&quot;[\\&quot;Monday\\&quot;,\\&quot;Tuesday\\&quot;]&quot;</span>\"\n        \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-11 17:58:53</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1138</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">performInsert</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">[object Illuminate\\Database\\Eloquent\\Builder]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>986</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">vendor/laravel/framework/src/Illuminate/Support/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>319</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\Database\\Eloquent\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">[object App\\Models\\Team]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>987</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">tap</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">[object App\\Models\\Team]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n        \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n        \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n        \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n        \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n        \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n        \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2330</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">forwardCallTo</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">[object Illuminate\\Database\\Eloquent\\Builder]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n          \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n          \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n          \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n          \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n          \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2342</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n          \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n          \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n          \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n          \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n          \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"39 characters\">app/Http/Controllers/TeamController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>272</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">__callStatic</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n          \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG.png</span>\"\n          \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"51 characters\">images/YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq.png</span>\"\n          \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n          \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n          \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n          \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>150</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Routing/Controller.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">createTeam</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Http\\Controllers\\TeamController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callAction</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Routing\\Controller</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">createTeam</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref>#46</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">json</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">convertedFiles</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#1751</a><samp data-depth=7 class=sf-dump-compact>\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"20 characters\">accuweather-logo.png</span>\"\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: \"<span class=sf-dump-str title=\"40 characters\">LQGZkAfsMXLpoy4qsWClrWogLxZI3XGXjTE6vpcG</span>\"\n              <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"12 characters\">C:\\xampp\\tmp</span>\"\n              <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D28.tmp</span>\"\n              <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D28.tmp</span>\"\n              <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D28.tmp</span>\"\n              <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n              <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\tmp\\php5D28.tmp\n24 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>tmp\\php5D28.tmp</span>\"\n              <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"**********\">2025-08-11 17:58:53</span>\n              <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n              <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n              <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>4503599627795477</span>\n              <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>6000</span>\n              <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n              <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n              <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n              <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n              <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D28.tmp</span>\"\n            </samp>}\n            \"<span class=sf-dump-key>logo</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#1750</a><samp data-depth=7 class=sf-dump-compact>\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"10 characters\">avatar.png</span>\"\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n              -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: \"<span class=sf-dump-str title=\"40 characters\">YuRXfnhkFSKJaBrOWPyk9LGNdKqOq3Bdwq8bVlOq</span>\"\n              <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"12 characters\">C:\\xampp\\tmp</span>\"\n              <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D29.tmp</span>\"\n              <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D29.tmp</span>\"\n              <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D29.tmp</span>\"\n              <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n              <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\tmp\\php5D29.tmp\n24 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>tmp\\php5D29.tmp</span>\"\n              <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"**********\">2025-08-11 17:58:53</span>\n              <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n              <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n              <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>1970324837399574</span>\n              <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>29876</span>\n              <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n              <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n              <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n              <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n              <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n              <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n              <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D29.tmp</span>\"\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">userResolver</span>: <span class=sf-dump-note>Closure($guard = null)</span> {<a class=sf-dump-ref>#1628</a><samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Auth\\AuthServiceProvider\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>AuthServiceProvider</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Auth\\AuthServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>AuthServiceProvider</span> {<a class=sf-dump-ref>#171</a> &#8230;}\n            <span class=sf-dump-meta>use</span>: {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>$app</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref>#2</a> &#8230;}\n            </samp>}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php\n119 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Auth\\AuthServiceProvider.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">88 to 90</span>\"\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">routeResolver</span>: <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#1638</a><samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Router\n25 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Router</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router</span> {<a class=sf-dump-ref>#27</a> &#8230;}\n            <span class=sf-dump-meta>use</span>: {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>$route</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref>#271</a> &#8230;}\n            </samp>}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php\n109 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">771 to 771</span>\"\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ParameterBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ParameterBag</span> {<a class=sf-dump-ref>#48</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#47</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n              \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n              \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n              \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n              \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n              \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n              \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n              \"<span class=sf-dump-key>created_by</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">query</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#54</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">server</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ServerBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ServerBag</span> {<a class=sf-dump-ref>#51</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:34</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"57 characters\">C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\public</span>\"\n              \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n              \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54468</span>\"\n              \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.0.30 Development Server</span>\"\n              \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n              \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n              \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n              \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/api/teams</span>\"\n              \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n              \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n              \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"67 characters\">C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\public\\index.php</span>\"\n              \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/api/teams</span>\"\n              \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/index.php/api/teams</span>\"\n              \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n              \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n              \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">37054</span>\"\n              \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">37054</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n              \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Bearer 433|PNWjra2rBuzPADQf2tOmLxHHnAJUULltoppLJabQ6b2369d9</span>\"\n              \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n              \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryE1TEeURarnw7XoDk</span>\"\n              \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryE1TEeURarnw7XoDk</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n              \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n              \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n              \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754913532.8345</span>\n              \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754913532</span>\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">files</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\FileBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileBag</span> {<a class=sf-dump-ref>#50</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\File\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation\\File</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#34</a><samp data-depth=8 class=sf-dump-compact>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"20 characters\">accuweather-logo.png</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"12 characters\">C:\\xampp\\tmp</span>\"\n                <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D28.tmp</span>\"\n                <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D28.tmp</span>\"\n                <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D28.tmp</span>\"\n                <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n                <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\tmp\\php5D28.tmp\n24 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>tmp\\php5D28.tmp</span>\"\n                <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"**********\">2025-08-11 17:58:53</span>\n                <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n                <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n                <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>4503599627795477</span>\n                <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>6000</span>\n                <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n                <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n                <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D28.tmp</span>\"\n              </samp>}\n              \"<span class=sf-dump-key>logo</span>\" => <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\File\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation\\File</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#35</a><samp data-depth=8 class=sf-dump-compact>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"10 characters\">avatar.png</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"12 characters\">C:\\xampp\\tmp</span>\"\n                <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D29.tmp</span>\"\n                <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">php5D29.tmp</span>\"\n                <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D29.tmp</span>\"\n                <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n                <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\tmp\\php5D29.tmp\n24 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>tmp\\php5D29.tmp</span>\"\n                <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"**********\">2025-08-11 17:58:53</span>\n                <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n                <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1754913532\">2025-08-11 17:58:52</span>\n                <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>1970324837399574</span>\n                <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>29876</span>\n                <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n                <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n                <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n                <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n                <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n                <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php5D29.tmp</span>\"\n              </samp>}\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">cookies</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#49</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\HeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HeaderBag</span> {<a class=sf-dump-ref>#52</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:17</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">37054</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">Bearer 433|PNWjra2rBuzPADQf2tOmLxHHnAJUULltoppLJabQ6b2369d9</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryE1TEeURarnw7XoDk</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">content</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">languages</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">charsets</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">encodings</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">acceptableContentTypes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">pathInfo</span>: \"<span class=sf-dump-str title=\"10 characters\">/api/teams</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">requestUri</span>: \"<span class=sf-dump-str title=\"10 characters\">/api/teams</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">baseUrl</span>: \"\"\n          #<span class=sf-dump-protected title=\"Protected property\">basePath</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">method</span>: \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">format</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">locale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">preferredFormat</span>: <span class=sf-dump-const>null</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isHostValid</span>: <span class=sf-dump-const>true</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isForwardedValid</span>: <span class=sf-dump-const>true</span>\n          <span class=sf-dump-meta>basePath</span>: \"\"\n          <span class=sf-dump-meta>format</span>: \"<span class=sf-dump-str title=\"4 characters\">html</span>\"\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>260</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"44 characters\">[object App\\Http\\Controllers\\TeamController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">createTeam</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>205</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>798</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">app/Http/Middleware/RoleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\RoleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"17 characters\">super-admin|admin</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>30</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"28 characters\">app/Http/Middleware/Cors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Http\\Middleware\\Cors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"79 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"79 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Illuminate\\Routing\\Middleware\\ThrottleRequests</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => {<a class=sf-dump-ref>#1687</a><samp data-depth=5 class=sf-dump-compact>\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">key</span>\": \"<span class=sf-dump-str title=\"40 characters\">13682ac418603aa0966369d46bbf282f562acf47</span>\"\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">maxAttempts</span>\": <span class=sf-dump-num>150</span>\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">decayMinutes</span>\": \"<span class=sf-dump-str>1</span>\"\n          +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">responseCallback</span>\": <span class=sf-dump-const>null</span>\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Illuminate\\Routing\\Middleware\\ThrottleRequests</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>1</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>44</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">sanctum</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>25</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Laravel\\Sanctum\\Http\\Middleware\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>799</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>776</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>740</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>729</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"28 characters\">app/Http/Middleware/Cors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Http\\Middleware\\Cors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>52</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"67 characters\">C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425201359\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                return true;\n", "            }\n", "\n", "            $statement = $this->getPdo()->prepare($query);\n", "\n", "            $this->bindValues($statement, $this->prepareBindings($bindings));\n", "\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FConnection.php&line=539", "ajax": false, "filename": "Connection.php", "line": "539"}}]}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/teams", "middleware": "api, auth:sanctum, cors, verified, role:super-admin|admin", "controller": "App\\Http\\Controllers\\TeamController@createTeam", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTeamController.php&line=218\" onclick=\"\">app/Http/Controllers/TeamController.php:218-289</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01755, "accumulated_duration_str": "17.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.211641, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '433' limit 1", "type": "query", "params": [], "bindings": ["433"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.21576, "duration": 0.01299, "duration_str": "12.99ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 74.017}, {"sql": "select * from `users` where `users`.`id` = 150 limit 1", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.232922, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "creative_app3", "explain": null, "start_percent": 74.017, "width_percent": 2.735}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-08-11 17:58:53', `personal_access_tokens`.`updated_at` = '2025-08-11 17:58:53' where `id` = 433", "type": "query", "params": [], "bindings": ["2025-08-11 17:58:53", "2025-08-11 17:58:53", 433], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.234989, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "creative_app3", "explain": null, "start_percent": 76.752, "width_percent": 8.319}, {"sql": "select `name` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 20, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": **********.243248, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "role:18", "source": {"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=18", "ajax": false, "filename": "RoleMiddleware.php", "line": "18"}, "connection": "creative_app3", "explain": null, "start_percent": 85.071, "width_percent": 9.231}, {"sql": "select exists(select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150 and `name` in ('super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member', 'guest')) as `exists`", "type": "query", "params": [], "bindings": [150, "super-admin", "admin", "hod", "manager", "team-lead", "coordinator", "shift-lead", "team-member", "guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 15, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": **********.247517, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "role:21", "source": {"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=21", "ajax": false, "filename": "RoleMiddleware.php", "line": "21"}, "connection": "creative_app3", "explain": null, "start_percent": 94.302, "width_percent": 1.709}, {"sql": "select count(*) as aggregate from `departments` where `id` = '7'", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 882}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 854}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}], "start": **********.2606661, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "creative_app3", "explain": null, "start_percent": 96.011, "width_percent": 1.823}, {"sql": "select exists(select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150 and `name` in ('super-admin', 'admin')) as `exists`", "type": "query", "params": [], "bindings": [150, "super-admin", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 246}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.26275, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "TeamController.php:246", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 246}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTeamController.php&line=246", "ajax": false, "filename": "TeamController.php", "line": "246"}, "connection": "creative_app3", "explain": null, "start_percent": 97.835, "width_percent": 2.165}]}, "models": {"data": {"Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/teams", "status_code": "<pre class=sf-dump id=sf-dump-935546843 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-935546843\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1134471515 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1134471515\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-642408500 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Clipcentric</span>\"\n  \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Shafayatul Islam</span>\"\n  \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Sha<PERSON>yatul Islam</span>\"\n  \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Shahinur Islam</span>\"\n  \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[&quot;Monday&quot;,&quot;Tuesday&quot;]</span>\"\n  \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n  \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>created_by</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642408500\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1559560910 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">37054</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 433|P******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryE1TEeURarnw7XoDk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1559560910\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-671587537 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-671587537\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1428605962 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 11:58:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">GET, POST, PUT, DELETE, OPTIONS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Content-Type, Authorization, X-Requested-With</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">149</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1428605962\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1876944530 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1876944530\", {\"maxDepth\":0})</script>\n"}}