import React, { useState, useEffect } from "react";
import {
  Eye,
  EyeOff,
  X,
  Shield,
  User,
  Lock,
  Building,
  Users,
  AlertCircle,
  CheckCircle,
  Wand2,
} from "lucide-react";
import {
  useCreatePasswordManagerMutation,
  useUpdatePasswordManagerMutation,
} from "../../features/api/passwordManagerApi";
import { useGetDepartmentsWithTeamsQuery } from "../../features/api/departmentApi";
import { useGetTeamsWithDepartmentsQuery } from "../../features/api/teamApi";

// Main component for the Add Password Card form
const AddPasswordCardForm = ({
  onCancel,
  generatedPassword,
  passwordStrength,
  editData = null, // For editing existing password
  onSuccess, // Callback after successful save
  departmentsData: propDepartmentsData = null,
  teamsData: propTeamsData = null,
}) => {
  // Use props if provided, otherwise fallback to API hooks
  const { data: departmentsDataApi, isLoading: isDepartmentsLoading, error: departmentsError } = useGetDepartmentsWithTeamsQuery(undefined, { skip: !!propDepartmentsData });
  const { data: teamsDataApi, isLoading: isTeamsLoading, error: teamsError } = useGetTeamsWithDepartmentsQuery(undefined, { skip: !!propTeamsData });
  const departmentsData = propDepartmentsData || departmentsDataApi;
  const teamsData = propTeamsData || teamsDataApi;
  const [createPasswordManager, { isLoading: isCreating }] =
    useCreatePasswordManagerMutation();
  const [updatePasswordManager, { isLoading: isUpdating }] =
    useUpdatePasswordManagerMutation();
  // console.clear();

  // console.log(departmentsData);

  // State to hold form data
  const [formData, setFormData] = useState({
    password_title: "",
    username: "",
    password: "",
    department_id: "",
    team_id: "",
  });

  // Initialize form data for editing or reset for new entries
  useEffect(() => {
    if (editData) {
      setFormData({
        password_title: editData.password_title || editData.title || "",
        username: editData.username || "",
        password: "", // Don't pre-fill password for security
        department_id: editData.department_id
          ? String(editData.department_id)
          : "",
        team_id: editData.team_id ? String(editData.team_id) : "",
      });
    } else {
      // FIXED: Reset form completely when not editing (new entry)
      setFormData({
        password_title: "",
        username: "",
        password: "",
        department_id: "",
        team_id: "",
      });
      setErrors({});
      setFormError("");
      setShowPassword(false);
    }
  }, [editData]);

  // State for showing/hiding the password, individual field errors, and a general form error
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [formError, setFormError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [focusedField, setFocusedField] = useState(null);

  // Effect to update the form's password field when a new password is generated
  useEffect(() => {
    if (generatedPassword) {
      setFormData((prev) => ({
        ...prev,
        password: generatedPassword,
        strength: passwordStrength,
      }));
    }
  }, [generatedPassword, passwordStrength]);

  // Handles input changes for all form fields
  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Reset team selection when the department changes
    if (name === "department_id") {
      setFormData((prev) => ({ ...prev, [name]: value, team_id: "" }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }

    // Clear the error for a field when the user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }

    // Clear form error when user starts typing
    if (formError) {
      setFormError("");
    }
  };

  // Calculates the strength of a given password
  const calculatePasswordStrength = (password) => {
    if (!password) return "Weak Password";
    let score = 0;
    if (password.length >= 12) score += 2;
    else if (password.length >= 8) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    if (password.length >= 16) score += 1;
    if (score >= 6) return "Strong Password";
    if (score >= 4) return "Moderate Password";
    return "Weak Password";
  };
  

  // Returns Tailwind CSS classes based on password strength for styling
  const getStrengthColor = (strength) => {
    switch (strength) {
      case "Strong Password":
        return "bg-emerald-50 text-emerald-700 border-emerald-200";
      case "Moderate Password":
        return "bg-amber-50 text-amber-700 border-amber-200";
      case "Weak Password":
        return "bg-red-50 text-red-700 border-red-200";
      default:
        return "bg-gray-50 text-gray-600 border-gray-200";
    }
  };

  // Get strength progress and color
  const getStrengthProgress = (strength) => {
    switch (strength) {
      case "Strong Password":
        return { width: "100%", color: "bg-emerald-500" };
      case "Moderate Password":
        return { width: "66%", color: "bg-amber-500" };
      case "Weak Password":
        return { width: "33%", color: "bg-red-500" };
      default:
        return { width: "0%", color: "bg-gray-300" };
    }
  };

  // Validates the form fields before submission
  const validateForm = () => {
    const newErrors = {};
    if (!formData.password_title.trim())
      newErrors.password_title = "Platform title is required";
    if (!formData.username.trim()) newErrors.username = "Username is required";
    if (!formData.password.trim()) newErrors.password = "Password is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handles the form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormError("");
    setIsLoading(true);

    if (validateForm()) {
      try {
        const submitData = {
          password_title: formData.password_title,
          username: formData.username,
          password: formData.password,
          department_id: formData.department_id
            ? parseInt(formData.department_id)
            : null,
          team_id: formData.team_id ? parseInt(formData.team_id) : null,
        };

        let newPasswordId = null;

        let result;
        if (editData) {
          // Update existing password
          result = await updatePasswordManager({
            id: editData.id,
            ...submitData,
          }).unwrap();
        } else {
          // Create new password
          result = await createPasswordManager(submitData).unwrap();
        }

        // Pass the new or updated item's ID to the success handler
        newPasswordId = result?.data?.id;

        // FIXED: Reset form after successful submission
        setFormData({
          password_title: "",
          username: "",
          password: "",
          department_id: "",
          team_id: "",
        });
        setErrors({});
        setFormError("");
        setShowPassword(false);

        // FIXED: Call success callback to refresh data and close modal
        // The success callback will handle the toast notification
        if (onSuccess) {
          onSuccess(newPasswordId);
        }
      } catch (error) {
        console.error("Error saving password:", error);
        setFormError(
          error?.data?.message || "Failed to save password. Please try again."
        );
      }
    } else {
      setFormError("Please fill out all required fields before saving.");
    }

    setIsLoading(false);
  };

  // Sets the form password to the generated password
  const useGeneratedPassword = () => {
    if (generatedPassword) {
      setFormData((prev) => ({
        ...prev,
        password: generatedPassword,
        strength: passwordStrength,
      }));
    }
  };

  const togglePasswordVisibility = () => setShowPassword(!showPassword);

  const hasErrors = Object.keys(errors).length > 0;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300">
      <div className="bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500">
        {/* Header - Responsive */}
        <div className="bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="p-1.5 sm:p-2 bg-white/20 rounded-lg">
                <Shield className="w-4 h-4 sm:w-6 sm:h-6" />
              </div>
              <div>
                <div className="text-left">
                  <h2 className="text-lg sm:text-xl font-semibold">
                    Add New Password
                  </h2>
                  <p className="text-white-100 text-xs sm:text-sm">
                    Secure your credentials with us
                  </p>
                </div>
              </div>
            </div>
            <button
              onClick={onCancel}
              className="p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200"
            >
              <X className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>
        </div>

        {/* Form Content - Responsive */}
        <div className="p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]">
          {/* Error Message - Responsive */}
          {formError && (
            <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2">
              <AlertCircle className="w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-left font-medium text-red-800 text-sm sm:text-base">
                  Action Required
                </p>
                <p className="text-red-700 text-xs sm:text-sm">{formError}</p>
              </div>
            </div>
          )}

          {/* Responsive Grid Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            {/* Department & Team Section - Responsive */}
            <div className="space-y-3 sm:space-y-4">
              <div
                className={`p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200 ${
                  hasErrors
                    ? "min-h-[300px] sm:h-[340px]"
                    : "min-h-[250px] sm:h-[280px]"
                }`}
              >
                <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                  <Building className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                  <h3 className="font-medium text-gray-800 text-sm sm:text-base">
                    Organization
                  </h3>
                </div>

                <div className="space-y-3 sm:space-y-4">
                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Department <span className="text-red-500">*</span>
                    </label>
                    <select
                      name="department_id"
                      value={formData.department_id}
                      onChange={handleInputChange}
                      onFocus={() => setFocusedField("department_id")}
                      onBlur={() => setFocusedField(null)}
                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none focus:ring-0 text-sm sm:text-base ${
                        errors.department_id
                          ? "border-red-300 focus:border-red-500"
                          : focusedField === "department_id"
                          ? "border-primary-500 shadow-lg shadow-primary/10-100"
                          : "border-gray-200 focus:border-primary hover:border-gray-300"
                      }`}
                    >
                      <option value="">Select Department</option>
                      {isDepartmentsLoading && <option disabled>Loading...</option>}
                      {departmentsError && <option disabled>Error loading departments</option>}
                      {departmentsData?.map((dept) => (
                        <option key={dept.id} value={dept.id}>
                          {dept.name}
                        </option>
                      ))}
                    </select>
                    {errors.department_id && (
                      <p className="mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        {errors.department_id}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Team <span className="text-red-500">*</span>
                    </label>
                    <select
                      name="team_id"
                      value={formData.team_id}
                      onChange={handleInputChange}
                      onFocus={() => setFocusedField("team_id")}
                      onBlur={() => setFocusedField(null)}
                      disabled={!formData.department_id}
                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${
                        !formData.department_id
                          ? "bg-gray-100 border-gray-200 cursor-not-allowed"
                          : errors.team_id
                          ? "border-red-300 focus:border-red-500"
                          : focusedField === "team_id"
                          ? "border-primary-500 shadow-lg shadow-primary-100"
                          : "border-gray-200 focus:border-primary-500 hover:border-gray-300"
                      }`}
                    >
                      <option value="">Select Team</option>
                      {isTeamsLoading && <option disabled>Loading...</option>}
                      {teamsError && <option disabled>Error loading teams</option>}
                      {formData.department_id &&
                        teamsData
                          ?.filter((team) =>
                            team.department_ids?.includes(
                              parseInt(formData.department_id)
                            )
                          )
                          .map((team) => (
                            <option key={team.id} value={team.id}>
                              {team.name}
                            </option>
                          ))}
                    </select>
                    {errors.team_id && (
                      <p className="mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        {errors.team_id}
                      </p>
                    )}
                    {!formData.department_id && (
                      <p className="mt-1.5 sm:mt-2 text-xs sm:text-sm text-gray-500 flex items-center">
                        <Users className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        Please select a department first
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Platform & Username Section - Responsive */}
            <div className="space-y-3 sm:space-y-4">
              <div
                className={`p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200 ${
                  hasErrors
                    ? "min-h-[300px] sm:h-[340px]"
                    : "min-h-[250px] sm:h-[280px]"
                }`}
              >
                <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                  <User className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                  <h3 className="font-medium text-gray-800 text-sm sm:text-base">
                    Account Details
                  </h3>
                </div>

                <div className="flex flex-col h-full space-y-3 sm:space-y-4">
                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Platform Title <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="password_title"
                      value={formData.password_title}
                      onChange={handleInputChange}
                      onFocus={() => setFocusedField("password_title")}
                      onBlur={() => setFocusedField(null)}
                      placeholder="e.g., Gmail, GitHub, AWS Console"
                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${
                        errors.password_title
                          ? "border-red-300 focus:border-red-500"
                          : focusedField === "password_title"
                          ? "border-primary-500 shadow-lg shadow-primary/10-100"
                          : "border-gray-200 focus:border-primary hover:border-gray-300"
                      }`}
                    />
                    {errors.password_title && (
                      <p className="mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        {errors.password_title}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                      Username <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      onFocus={() => setFocusedField("username")}
                      onBlur={() => setFocusedField(null)}
                      placeholder="Enter username or email"
                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${
                        errors.username
                          ? "border-red-300 focus:border-red-500"
                          : focusedField === "username"
                          ? "border-primary-500 shadow-lg shadow-primary/10-100"
                          : "border-gray-200 focus:border-primary hover:border-gray-300"
                      }`}
                    />
                    {errors.username && (
                      <p className="mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        {errors.username}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Password Section - Responsive */}
          <div className="mt-4 sm:mt-6">
            <div className="p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200">
              <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                <Lock className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                <h3 className="font-medium text-gray-800 text-sm sm:text-base">
                  Security
                </h3>
              </div>

              <div>
                <label className="text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                  Password <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    onFocus={() => setFocusedField("password")}
                    onBlur={() => setFocusedField(null)}
                    placeholder="Enter password"
                    className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-20 sm:pr-32 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${
                      errors.password
                        ? "border-red-300 focus:border-red-500"
                        : focusedField === "password"
                        ? "border-primary-500 shadow-lg shadow-primary/10-100"
                        : "border-gray-200 focus:border-primary-500 hover:border-gray-300"
                    }`}
                  />

                  {/* Responsive Button Container */}
                  <div className="absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 flex items-center space-x-0.5 sm:space-x-1">
                    <button
                      type="button"
                      onClick={useGeneratedPassword}
                      disabled={!generatedPassword}
                      className={`px-2 sm:px-3 py-1 sm:py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-0.5 sm:space-x-1 ${
                        generatedPassword
                          ? "bg-primary/10-100 text-white-700 hover:bg-primary-200"
                          : "bg-gray-100 text-gray-400 cursor-not-allowed"
                      }`}
                      title="Use generated password"
                    >
                      <Wand2 className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                      <span className="hidden sm:inline">Use</span>
                    </button>

                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="p-1 sm:p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200"
                    >
                      {showPassword ? (
                        <EyeOff className="w-3 h-3 sm:w-4 sm:h-4" />
                      ) : (
                        <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                      )}
                    </button>
                  </div>
                </div>

                {errors.password && (
                  <p className="mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center">
                    <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                    {errors.password}
                  </p>
                )}

                {/* Password Strength Indicator - Responsive */}
                {formData.password && (
                  <div className="mt-3 sm:mt-4 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs sm:text-sm font-medium text-gray-700">
                        Password Strength
                      </span>
                      <span
                        className={`text-xs sm:text-sm font-medium px-2 py-1 rounded-full ${getStrengthColor(
                          formData.strength
                        )}`}
                      >
                        {formData.strength}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5 sm:h-2">
                      <div
                        className={`h-1.5 sm:h-2 rounded-full transition-all duration-300 ${
                          getStrengthProgress(formData.strength).color
                        }`}
                        style={{
                          width: getStrengthProgress(formData.strength).width,
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer - Responsive */}
        <div className="px-3 sm:px-6 py-3 sm:py-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
          <div className="flex items-center space-x-2 text-xs sm:text-sm text-gray-500">
            <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4" />
            <span>All fields marked with * are required</span>
          </div>

          {/* Responsive Button Container */}
          <div className="flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto">
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200 font-medium text-sm sm:text-base"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isLoading || isCreating || isUpdating}
              className="flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-2.5 bg-primary hover:bg-primary/90 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-500 focus:ring-offset-2 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 sm:space-x-2 text-sm sm:text-base"
            >
              {isLoading || isCreating || isUpdating ? (
                <>
                  <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span className="hidden sm:inline">
                    {editData ? "Updating..." : "Saving..."}
                  </span>
                  <span className="sm:hidden">
                    {editData ? "Update" : "Save"}
                  </span>
                </>
              ) : (
                <>
                  <Shield className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">
                    {editData ? "Update Password" : "Save Password"}
                  </span>
                  <span className="sm:hidden">
                    {editData ? "Update" : "Save"}
                  </span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddPasswordCardForm;