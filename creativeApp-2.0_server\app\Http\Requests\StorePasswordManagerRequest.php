<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class StorePasswordManagerRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $userId = $this->input('user_id') ?? auth()->id();

        return [
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
            'user_id' => 'nullable|exists:users,id',
            'password_title' => 'required|string|max:255',
            'username' => 'required|string|max:255',
            'password' => 'required|string|max:1000',
        ];
    }



    public function messages(): array
    {
        return [
            'department_id.exists' => 'The selected department does not exist.',
            'team_id.exists' => 'The selected team does not exist.',
            'user_id.exists' => 'The selected user does not exist.',
            'password_title.required' => 'Platform/Service title is required.',
            'password_title.max' => 'Platform/Service title may not be greater than 255 characters.',
            'username.required' => 'Username is required.',
            'username.max' => 'Username may not be greater than 255 characters.',
            'password.required' => 'Password is required.',
            'password.min' => 'Password must be at least 1 character.',
            'password.max' => 'Password may not be greater than 1000 characters.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422)
        );
    }
}