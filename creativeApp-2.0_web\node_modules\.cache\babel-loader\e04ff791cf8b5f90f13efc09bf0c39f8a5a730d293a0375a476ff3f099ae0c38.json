{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\team\\\\AddTeam.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { alertMessage } from '../../common/coreui';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\nconst isTokenValid = () => {\n  const token = localStorage.getItem('token');\n  return token !== null;\n};\nconst AddTeam = ({\n  isVisible,\n  setVisible\n}) => {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [departments, setDepartments] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [teamName, setTeamName] = useState('');\n  const [icon, setIcon] = useState(null);\n  const [logo, setLogo] = useState(null);\n  const [poc, setPoc] = useState('');\n  const [manager, setManager] = useState('');\n  const [teamLead, setTeamLead] = useState('');\n  const [launch, setLaunch] = useState('');\n  const [workday, setWorkday] = useState([]);\n  const [departmentId, setDepartmentId] = useState('');\n  const [error, setError] = useState('');\n  const [successMessage, setSuccessMessage] = useState('');\n  const [loggedInUser, setLoggedInUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Days of the week for multi-select\n  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\n\n  // Handle workday selection\n  const handleWorkdayChange = day => {\n    setWorkday(prev => {\n      if (prev.includes(day)) {\n        return prev.filter(d => d !== day);\n      } else {\n        return [...prev, day];\n      }\n    });\n  };\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!isTokenValid()) {\n        setError('No authentication token found.');\n        setLoading(false);\n        return;\n      }\n      const token = localStorage.getItem('token');\n      try {\n        // Fetch Users\n        const usersResponse = await fetch(`${API_URL}/users`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!usersResponse.ok) {\n          throw new Error('Failed to fetch users');\n        }\n        const usersData = await usersResponse.json();\n        setUsers(usersData.map(user => ({\n          id: user.id,\n          fullName: `${user.fname || ''} ${user.lname || ''}`.trim()\n        })));\n\n        // Fetch Departments\n        const departmentsResponse = await fetch(`${API_URL}/departments`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!departmentsResponse.ok) {\n          throw new Error('Failed to fetch departments');\n        }\n        const departmentsData = await departmentsResponse.json();\n        setDepartments(departmentsData.departments); // Assuming the response has 'departments' array\n      } catch (error) {\n        setError(error.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n\n  // Fetch logged-in user data (user_id)\n  useEffect(() => {\n    const userId = localStorage.getItem('user_id');\n    if (userId) {\n      setLoggedInUser(userId);\n    }\n  }, []);\n  const handleSubmit = async event => {\n    event.preventDefault(); // Prevent default form submission behavior\n\n    // Get user_id from localStorage for 'created_by'\n    const createdBy = loggedInUser;\n    if (!createdBy) {\n      setError('User is not logged in.');\n      return;\n    }\n    const trimmedTeamName = teamName.trim();\n\n    // Check if the team already exists\n    const teamExists = teams.some(team => {\n      const teamNameLower = team.name.toLowerCase().trim();\n      return teamNameLower === trimmedTeamName.toLowerCase();\n    });\n    if (teamExists) {\n      setError('Team already exists. Please add a different team.');\n      setTimeout(() => setError(''), 3000);\n      return; // Exit if the team already exists\n    }\n    setError(''); // Clear any previous error\n\n    try {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        setError('Authentication token is missing.');\n        return; // Exit if token is not available\n      }\n      const formData = new FormData();\n      formData.append('name', trimmedTeamName);\n      formData.append('icon', icon);\n      formData.append('logo', logo);\n      formData.append('poc', poc);\n      formData.append('manager', manager);\n      formData.append('team_lead', teamLead);\n      formData.append('workday', workday.join(', '));\n      formData.append('launch', launch);\n      formData.append('department_id', departmentId);\n      formData.append('created_by', createdBy);\n      const response = await fetch(`${API_URL}/teams`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        },\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to save team: ' + response.statusText);\n      }\n      const result = await response.json();\n      //setSuccessMessage(`Team \"${result.name || trimmedTeamName}\" added successfully!`);\n      alertMessage('success');\n      setTeamName('');\n      setIcon(null);\n      setLogo(null);\n      setPoc('');\n      setManager('');\n      setTeamLead('');\n      setLaunch('');\n      setWorkday([]);\n      setDepartmentId('');\n\n      // Refetch the teams list\n      const newTeamsResponse = await fetch(`${API_URL}/teams`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!newTeamsResponse.ok) {\n        throw new Error('Failed to fetch teams: ' + newTeamsResponse.statusText);\n      }\n      const newTeamsData = await newTeamsResponse.json();\n      setTeams(newTeamsData.teams); // Update the teams list\n    } catch (error) {\n      setError(error.message || 'Failed to add team.');\n      console.error('Error adding team:', error);\n    }\n  };\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md w-full max-w-3xl relative overflow-y-auto h-[80vh] mt-10 scrollbar-vertical\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-4 bg-gray-100 p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-xl text-left font-medium text-gray-800\",\n            children: \"Add New Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setVisible(false),\n            className: \"text-3xl text-gray-500 hover:text-gray-800\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"text-left p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"department\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"department\",\n                value: departmentId,\n                onChange: e => setDepartmentId(e.target.value),\n                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Department\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 37\n                }, this), departments.map(department => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: department.id,\n                  children: department.name\n                }, department.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 41\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"teamName\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Team Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"teamName\",\n                value: teamName,\n                onChange: e => setTeamName(e.target.value),\n                required: true,\n                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 33\n              }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500 text-sm\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 43\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"icon\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"icon\",\n                onChange: e => setIcon(e.target.files[0]),\n                accept: \"image/*\",\n                required: true,\n                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"logo\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"logo\",\n                onChange: e => setLogo(e.target.files[0]),\n                accept: \"image/*\",\n                required: true,\n                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"poc\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Point of Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"poc\",\n                value: poc,\n                onChange: e => setPoc(e.target.value),\n                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select POC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 37\n                }, this), users.map(user => user.fullName && /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: user.fullName,\n                  children: user.fullName\n                }, user.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 41\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"manager\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Manager\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"manager\",\n                value: manager,\n                onChange: e => setManager(e.target.value),\n                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Manager\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 37\n                }, this), users.map(user => user.fullName && /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: user.fullName,\n                  children: user.fullName\n                }, user.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 41\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"teamLead\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Team Lead\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"teamLead\",\n                value: teamLead,\n                onChange: e => setTeamLead(e.target.value),\n                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Team Lead\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 37\n                }, this), users.map(user => user.fullName && /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: user.fullName,\n                  children: user.fullName\n                }, user.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 41\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Work Days\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-2\",\n                children: daysOfWeek.map(day => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"flex items-center space-x-2 cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: workday.includes(day),\n                    onChange: () => handleWorkdayChange(day),\n                    className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-700\",\n                    children: day\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 45\n                  }, this)]\n                }, day, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 33\n              }, this), workday.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-2\",\n                children: [\"Selected: \", workday.join(', ')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"launch\",\n                className: \"block text-sm font-medium text-gray-700 pb-4\",\n                children: \"Launch Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                id: \"launch\",\n                value: launch,\n                onChange: e => setLaunch(e.target.value),\n                className: \"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 25\n          }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 35\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-left pt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                class: \"material-symbols-rounded text-white text-xl font-regular\",\n                children: \"add_circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 33\n              }, this), loading ? 'Adding...' : 'Add Team']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 25\n          }, this), successMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-green-500 text-sm\",\n            children: successMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 44\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(AddTeam, \"rlEkPGQMvNM+eo4ZqszuWfAseXs=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = AddTeam;\nexport default AddTeam;\nvar _c;\n$RefreshReg$(_c, \"AddTeam\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useLocation", "useNavigate", "alertMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_URL", "process", "env", "REACT_APP_BASE_API_URL", "isTokenValid", "token", "localStorage", "getItem", "AddTeam", "isVisible", "setVisible", "_s", "location", "navigate", "users", "setUsers", "departments", "setDepartments", "teams", "setTeams", "teamName", "setTeamName", "icon", "setIcon", "logo", "set<PERSON><PERSON>", "poc", "setPoc", "manager", "setManager", "teamLead", "setTeamLead", "launch", "setLaunch", "workday", "setWorkday", "departmentId", "setDepartmentId", "error", "setError", "successMessage", "setSuccessMessage", "loggedInUser", "setLoggedInUser", "loading", "setLoading", "daysOfWeek", "handleWorkdayChange", "day", "prev", "includes", "filter", "d", "fetchData", "usersResponse", "fetch", "method", "headers", "ok", "Error", "usersData", "json", "map", "user", "id", "fullName", "fname", "lname", "trim", "departmentsResponse", "departmentsData", "message", "userId", "handleSubmit", "event", "preventDefault", "created<PERSON>y", "trimmedTeamName", "teamExists", "some", "team", "teamNameLower", "name", "toLowerCase", "setTimeout", "formData", "FormData", "append", "join", "response", "body", "errorData", "statusText", "result", "newTeamsResponse", "newTeamsData", "console", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "value", "onChange", "e", "target", "required", "department", "type", "files", "accept", "checked", "length", "class", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/pages/team/AddTeam.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport { alertMessage } from '../../common/coreui';\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst isTokenValid = () => {\r\n    const token = localStorage.getItem('token');\r\n    return token !== null;\r\n};\r\n\r\nconst AddTeam = ({isVisible, setVisible}) => {\r\n    const location = useLocation();\r\n    const navigate = useNavigate();\r\n    const [users, setUsers] = useState([]);\r\n    const [departments, setDepartments] = useState([]);\r\n    const [teams, setTeams] = useState([]);\r\n    const [teamName, setTeamName] = useState('');\r\n    const [icon, setIcon] = useState(null);\r\n    const [logo, setLogo] = useState(null);\r\n    const [poc, setPoc] = useState('');\r\n    const [manager, setManager] = useState('');\r\n    const [teamLead, setTeamLead] = useState('');\r\n    const [launch, setLaunch] = useState('');\r\n    const [workday, setWorkday] = useState([]);\r\n    const [departmentId, setDepartmentId] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n    const [loggedInUser, setLoggedInUser] = useState(null);\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Days of the week for multi-select\r\n    const daysOfWeek = [\r\n        'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'\r\n    ];\r\n\r\n    // Handle workday selection\r\n    const handleWorkdayChange = (day) => {\r\n        setWorkday(prev => {\r\n            if (prev.includes(day)) {\r\n                return prev.filter(d => d !== day);\r\n            } else {\r\n                return [...prev, day];\r\n            }\r\n        });\r\n    };\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            if (!isTokenValid()) {\r\n                setError('No authentication token found.');\r\n                setLoading(false);\r\n                return;\r\n            }\r\n\r\n            const token = localStorage.getItem('token');\r\n\r\n            try {\r\n                // Fetch Users\r\n                const usersResponse = await fetch(`${API_URL}/users`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!usersResponse.ok) {\r\n                    throw new Error('Failed to fetch users');\r\n                }\r\n\r\n                const usersData = await usersResponse.json();\r\n                setUsers(usersData.map(user => ({\r\n                    id: user.id,\r\n                    fullName: `${user.fname || ''} ${user.lname || ''}`.trim(),\r\n                })));\r\n\r\n                // Fetch Departments\r\n                const departmentsResponse = await fetch(`${API_URL}/departments`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!departmentsResponse.ok) {\r\n                    throw new Error('Failed to fetch departments');\r\n                }\r\n\r\n                const departmentsData = await departmentsResponse.json();\r\n                setDepartments(departmentsData.departments); // Assuming the response has 'departments' array\r\n            } catch (error) {\r\n                setError(error.message);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, []);\r\n\r\n    // Fetch logged-in user data (user_id)\r\n    useEffect(() => {\r\n        const userId = localStorage.getItem('user_id');\r\n        if (userId) {\r\n            setLoggedInUser(userId);\r\n        }\r\n    }, []);\r\n\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault(); // Prevent default form submission behavior\r\n\r\n        // Get user_id from localStorage for 'created_by'\r\n        const createdBy = loggedInUser;\r\n\r\n        if (!createdBy) {\r\n            setError('User is not logged in.');\r\n            return;\r\n        }\r\n    \r\n        const trimmedTeamName = teamName.trim();\r\n    \r\n        // Check if the team already exists\r\n        const teamExists = teams.some((team) => {\r\n            const teamNameLower = team.name.toLowerCase().trim();\r\n            return teamNameLower === trimmedTeamName.toLowerCase();\r\n        });\r\n    \r\n        if (teamExists) {\r\n            setError('Team already exists. Please add a different team.');\r\n            setTimeout(() => setError(''), 3000);\r\n            return; // Exit if the team already exists\r\n        }\r\n    \r\n        setError(''); // Clear any previous error\r\n    \r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('Authentication token is missing.');\r\n                return; // Exit if token is not available\r\n            }\r\n    \r\n            const formData = new FormData();\r\n            formData.append('name', trimmedTeamName);\r\n            formData.append('icon', icon);\r\n            formData.append('logo', logo);\r\n            formData.append('poc', poc);\r\n            formData.append('manager', manager);\r\n            formData.append('team_lead', teamLead);\r\n            formData.append('workday', workday.join(', '));\r\n            formData.append('launch', launch);\r\n            formData.append('department_id', departmentId);\r\n            formData.append('created_by', createdBy);\r\n\r\n    \r\n            const response = await fetch(`${API_URL}/teams`, {\r\n                method: 'POST',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                },\r\n                body: formData,\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                const errorData = await response.json();\r\n                throw new Error(errorData.error || 'Failed to save team: ' + response.statusText);\r\n            }\r\n    \r\n            const result = await response.json();\r\n            //setSuccessMessage(`Team \"${result.name || trimmedTeamName}\" added successfully!`);\r\n            alertMessage('success');\r\n\r\n            setTeamName('');\r\n            setIcon(null);\r\n            setLogo(null);\r\n            setPoc('');\r\n            setManager('');\r\n            setTeamLead('');\r\n            setLaunch('');\r\n            setWorkday([]);\r\n            setDepartmentId('');\r\n    \r\n            // Refetch the teams list\r\n            const newTeamsResponse = await fetch(`${API_URL}/teams`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n    \r\n            if (!newTeamsResponse.ok) {\r\n                throw new Error('Failed to fetch teams: ' + newTeamsResponse.statusText);\r\n            }\r\n    \r\n            const newTeamsData = await newTeamsResponse.json();\r\n            setTeams(newTeamsData.teams); // Update the teams list\r\n        } catch (error) {\r\n            setError(error.message || 'Failed to add team.');\r\n            console.error('Error adding team:', error);\r\n        }\r\n    };\r\n    \r\n\r\n    if (!isVisible) return null;\r\n\r\n    return (\r\n        <>\r\n            \r\n            <div className=\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\">\r\n                <div className=\"bg-white rounded-lg shadow-md w-full max-w-3xl relative overflow-y-auto h-[80vh] mt-10 scrollbar-vertical\">\r\n                    <div className=\"flex justify-between items-center mb-4 bg-gray-100 p-4\">\r\n                        <h4 className=\"text-xl text-left font-medium text-gray-800\">Add New Team</h4>\r\n                        <button onClick={() => setVisible(false)}\r\n                            className=\"text-3xl text-gray-500 hover:text-gray-800\"\r\n                            \r\n                        >\r\n                            &times;\r\n                        </button>\r\n                    </div>\r\n                    <form onSubmit={handleSubmit} className=\"text-left p-6\">\r\n                        <div className=''>                           \r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"department\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Department\r\n                                </label>\r\n                                <select\r\n                                    id=\"department\"\r\n                                    value={departmentId}\r\n                                    onChange={(e) => setDepartmentId(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select Department</option>\r\n                                    {departments.map((department) => (\r\n                                        <option key={department.id} value={department.id}>\r\n                                            {department.name}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                            </div>\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"teamName\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Team Name\r\n                                </label>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    id=\"teamName\"\r\n                                    value={teamName}\r\n                                    onChange={(e) => setTeamName(e.target.value)}\r\n                                    required\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                />\r\n                                {error && <p className=\"text-red-500 text-sm\">{error}</p>}\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"icon\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Icon\r\n                                </label>\r\n                                <input\r\n                                    type=\"file\"\r\n                                    id=\"icon\"\r\n                                    onChange={(e) => setIcon(e.target.files[0])}\r\n                                    accept=\"image/*\"\r\n                                    required\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"logo\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Logo\r\n                                </label>\r\n                                <input\r\n                                    type=\"file\"\r\n                                    id=\"logo\"\r\n                                    onChange={(e) => setLogo(e.target.files[0])}\r\n                                    accept=\"image/*\"\r\n                                    required\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"poc\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Point of Contact\r\n                                </label>\r\n                                <select\r\n                                    id=\"poc\"\r\n                                    value={poc}\r\n                                    onChange={(e) => setPoc(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select POC</option>\r\n                                    {users.map((user) => user.fullName && (\r\n                                        <option key={user.id} value={user.fullName}>\r\n                                            {user.fullName}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"manager\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Manager\r\n                                </label>\r\n                                <select\r\n                                    id=\"manager\"\r\n                                    value={manager}\r\n                                    onChange={(e) => setManager(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select Manager</option>\r\n                                    {users.map((user) => user.fullName && (\r\n                                        <option key={user.id} value={user.fullName}>\r\n                                            {user.fullName}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"teamLead\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Team Lead\r\n                                </label>\r\n                                <select\r\n                                    id=\"teamLead\"\r\n                                    value={teamLead}\r\n                                    onChange={(e) => setTeamLead(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select Team Lead</option>\r\n                                    {users.map((user) => user.fullName && (\r\n                                        <option key={user.id} value={user.fullName}>\r\n                                            {user.fullName}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Work Days\r\n                                </label>\r\n                                <div className=\"grid grid-cols-2 gap-2\">\r\n                                    {daysOfWeek.map((day) => (\r\n                                        <label key={day} className=\"flex items-center space-x-2 cursor-pointer\">\r\n                                            <input\r\n                                                type=\"checkbox\"\r\n                                                checked={workday.includes(day)}\r\n                                                onChange={() => handleWorkdayChange(day)}\r\n                                                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                                            />\r\n                                            <span className=\"text-sm text-gray-700\">{day}</span>\r\n                                        </label>\r\n                                    ))}\r\n                                </div>\r\n                                {workday.length > 0 && (\r\n                                    <p className=\"text-xs text-gray-500 mt-2\">\r\n                                        Selected: {workday.join(', ')}\r\n                                    </p>\r\n                                )}\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"launch\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Launch Date\r\n                                </label>\r\n                                <input\r\n                                    type=\"date\"\r\n                                    id=\"launch\"\r\n                                    value={launch}\r\n                                    onChange={(e) => setLaunch(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n\r\n                        {error && <p className=\"text-red-500 text-sm\">{error}</p>}\r\n                        \r\n                        <div className='text-left pt-6'>\r\n                            <button\r\n                                type=\"submit\"\r\n                                className=\"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\"\r\n                            >\r\n                                <span class=\"material-symbols-rounded text-white text-xl font-regular\">add_circle</span>\r\n                                {loading ? 'Adding...' : 'Add Team'}\r\n                            </button>\r\n                        </div>\r\n\r\n                        {successMessage && <p className=\"text-green-500 text-sm\">{successMessage}</p>}\r\n                    </form>\r\n                </div>\r\n            </div>\r\n          \r\n        </>\r\n    );\r\n};\r\n\r\nexport default AddTeam;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;AAElD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,KAAK,IAAI;AACzB,CAAC;AAED,MAAMG,OAAO,GAAGA,CAAC;EAACC,SAAS;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8B,IAAI,EAAEC,OAAO,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgC,IAAI,EAAEC,OAAO,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACkC,GAAG,EAAEC,MAAM,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwC,MAAM,EAAEC,SAAS,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMsD,UAAU,GAAG,CACf,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAC/E;;EAED;EACA,MAAMC,mBAAmB,GAAIC,GAAG,IAAK;IACjCb,UAAU,CAACc,IAAI,IAAI;MACf,IAAIA,IAAI,CAACC,QAAQ,CAACF,GAAG,CAAC,EAAE;QACpB,OAAOC,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,GAAG,CAAC;MACtC,CAAC,MAAM;QACH,OAAO,CAAC,GAAGC,IAAI,EAAED,GAAG,CAAC;MACzB;IACJ,CAAC,CAAC;EACN,CAAC;EAEDzD,SAAS,CAAC,MAAM;IACZ,MAAM8D,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAI,CAACjD,YAAY,CAAC,CAAC,EAAE;QACjBmC,QAAQ,CAAC,gCAAgC,CAAC;QAC1CM,UAAU,CAAC,KAAK,CAAC;QACjB;MACJ;MAEA,MAAMxC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAI;QACA;QACA,MAAM+C,aAAa,GAAG,MAAMC,KAAK,CAAC,GAAGvD,OAAO,QAAQ,EAAE;UAClDwD,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACL,eAAe,EAAE,UAAUpD,KAAK,EAAE;YAClC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEF,IAAI,CAACiD,aAAa,CAACI,EAAE,EAAE;UACnB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;QAC5C;QAEA,MAAMC,SAAS,GAAG,MAAMN,aAAa,CAACO,IAAI,CAAC,CAAC;QAC5C9C,QAAQ,CAAC6C,SAAS,CAACE,GAAG,CAACC,IAAI,KAAK;UAC5BC,EAAE,EAAED,IAAI,CAACC,EAAE;UACXC,QAAQ,EAAE,GAAGF,IAAI,CAACG,KAAK,IAAI,EAAE,IAAIH,IAAI,CAACI,KAAK,IAAI,EAAE,EAAE,CAACC,IAAI,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC;;QAEJ;QACA,MAAMC,mBAAmB,GAAG,MAAMd,KAAK,CAAC,GAAGvD,OAAO,cAAc,EAAE;UAC9DwD,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACL,eAAe,EAAE,UAAUpD,KAAK,EAAE;YAClC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEF,IAAI,CAACgE,mBAAmB,CAACX,EAAE,EAAE;UACzB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;QAClD;QAEA,MAAMW,eAAe,GAAG,MAAMD,mBAAmB,CAACR,IAAI,CAAC,CAAC;QACxD5C,cAAc,CAACqD,eAAe,CAACtD,WAAW,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,OAAOsB,KAAK,EAAE;QACZC,QAAQ,CAACD,KAAK,CAACiC,OAAO,CAAC;MAC3B,CAAC,SAAS;QACN1B,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ,CAAC;IAEDQ,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9D,SAAS,CAAC,MAAM;IACZ,MAAMiF,MAAM,GAAGlE,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAC9C,IAAIiE,MAAM,EAAE;MACR7B,eAAe,CAAC6B,MAAM,CAAC;IAC3B;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAG,MAAOC,KAAK,IAAK;IAClCA,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;;IAExB;IACA,MAAMC,SAAS,GAAGlC,YAAY;IAE9B,IAAI,CAACkC,SAAS,EAAE;MACZrC,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACJ;IAEA,MAAMsC,eAAe,GAAGzD,QAAQ,CAACgD,IAAI,CAAC,CAAC;;IAEvC;IACA,MAAMU,UAAU,GAAG5D,KAAK,CAAC6D,IAAI,CAAEC,IAAI,IAAK;MACpC,MAAMC,aAAa,GAAGD,IAAI,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACf,IAAI,CAAC,CAAC;MACpD,OAAOa,aAAa,KAAKJ,eAAe,CAACM,WAAW,CAAC,CAAC;IAC1D,CAAC,CAAC;IAEF,IAAIL,UAAU,EAAE;MACZvC,QAAQ,CAAC,mDAAmD,CAAC;MAC7D6C,UAAU,CAAC,MAAM7C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACpC,OAAO,CAAC;IACZ;IAEAA,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd,IAAI;MACA,MAAMlC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACRkC,QAAQ,CAAC,kCAAkC,CAAC;QAC5C,OAAO,CAAC;MACZ;MAEA,MAAM8C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEV,eAAe,CAAC;MACxCQ,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEjE,IAAI,CAAC;MAC7B+D,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE/D,IAAI,CAAC;MAC7B6D,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAE7D,GAAG,CAAC;MAC3B2D,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE3D,OAAO,CAAC;MACnCyD,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEzD,QAAQ,CAAC;MACtCuD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAErD,OAAO,CAACsD,IAAI,CAAC,IAAI,CAAC,CAAC;MAC9CH,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEvD,MAAM,CAAC;MACjCqD,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEnD,YAAY,CAAC;MAC9CiD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEX,SAAS,CAAC;MAGxC,MAAMa,QAAQ,GAAG,MAAMlC,KAAK,CAAC,GAAGvD,OAAO,QAAQ,EAAE;QAC7CwD,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUpD,KAAK;QACpC,CAAC;QACDqF,IAAI,EAAEL;MACV,CAAC,CAAC;MAEF,IAAI,CAACI,QAAQ,CAAC/B,EAAE,EAAE;QACd,MAAMiC,SAAS,GAAG,MAAMF,QAAQ,CAAC5B,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIF,KAAK,CAACgC,SAAS,CAACrD,KAAK,IAAI,uBAAuB,GAAGmD,QAAQ,CAACG,UAAU,CAAC;MACrF;MAEA,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAAC5B,IAAI,CAAC,CAAC;MACpC;MACAlE,YAAY,CAAC,SAAS,CAAC;MAEvB0B,WAAW,CAAC,EAAE,CAAC;MACfE,OAAO,CAAC,IAAI,CAAC;MACbE,OAAO,CAAC,IAAI,CAAC;MACbE,MAAM,CAAC,EAAE,CAAC;MACVE,UAAU,CAAC,EAAE,CAAC;MACdE,WAAW,CAAC,EAAE,CAAC;MACfE,SAAS,CAAC,EAAE,CAAC;MACbE,UAAU,CAAC,EAAE,CAAC;MACdE,eAAe,CAAC,EAAE,CAAC;;MAEnB;MACA,MAAMyD,gBAAgB,GAAG,MAAMvC,KAAK,CAAC,GAAGvD,OAAO,QAAQ,EAAE;QACrDwD,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUpD,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACyF,gBAAgB,CAACpC,EAAE,EAAE;QACtB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,GAAGmC,gBAAgB,CAACF,UAAU,CAAC;MAC5E;MAEA,MAAMG,YAAY,GAAG,MAAMD,gBAAgB,CAACjC,IAAI,CAAC,CAAC;MAClD1C,QAAQ,CAAC4E,YAAY,CAAC7E,KAAK,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACiC,OAAO,IAAI,qBAAqB,CAAC;MAChDyB,OAAO,CAAC1D,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC9C;EACJ,CAAC;EAGD,IAAI,CAAC7B,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACIZ,OAAA,CAAAE,SAAA;IAAAkG,QAAA,eAEIpG,OAAA;MAAKqG,SAAS,EAAC,kHAAkH;MAAAD,QAAA,eAC7HpG,OAAA;QAAKqG,SAAS,EAAC,2GAA2G;QAAAD,QAAA,gBACtHpG,OAAA;UAAKqG,SAAS,EAAC,wDAAwD;UAAAD,QAAA,gBACnEpG,OAAA;YAAIqG,SAAS,EAAC,6CAA6C;YAAAD,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EzG,OAAA;YAAQ0G,OAAO,EAAEA,CAAA,KAAM7F,UAAU,CAAC,KAAK,CAAE;YACrCwF,SAAS,EAAC,4CAA4C;YAAAD,QAAA,EAEzD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACNzG,OAAA;UAAM2G,QAAQ,EAAE/B,YAAa;UAACyB,SAAS,EAAC,eAAe;UAAAD,QAAA,gBACnDpG,OAAA;YAAKqG,SAAS,EAAC,EAAE;YAAAD,QAAA,gBACbpG,OAAA;cAAKqG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACjBpG,OAAA;gBAAO4G,OAAO,EAAC,YAAY;gBAACP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAErF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzG,OAAA;gBACImE,EAAE,EAAC,YAAY;gBACf0C,KAAK,EAAEtE,YAAa;gBACpBuE,QAAQ,EAAGC,CAAC,IAAKvE,eAAe,CAACuE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACjDR,SAAS,EAAC,8HAA8H;gBACxIY,QAAQ;gBAAAb,QAAA,gBAERpG,OAAA;kBAAQ6G,KAAK,EAAC,EAAE;kBAAAT,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC1CtF,WAAW,CAAC8C,GAAG,CAAEiD,UAAU,iBACxBlH,OAAA;kBAA4B6G,KAAK,EAAEK,UAAU,CAAC/C,EAAG;kBAAAiC,QAAA,EAC5Cc,UAAU,CAAC7B;gBAAI,GADP6B,UAAU,CAAC/C,EAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAElB,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACNzG,OAAA;cAAKqG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACjBpG,OAAA;gBAAO4G,OAAO,EAAC,UAAU;gBAACP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzG,OAAA;gBACImH,IAAI,EAAC,MAAM;gBACXhD,EAAE,EAAC,UAAU;gBACb0C,KAAK,EAAEtF,QAAS;gBAChBuF,QAAQ,EAAGC,CAAC,IAAKvF,WAAW,CAACuF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC7CI,QAAQ;gBACRZ,SAAS,EAAC;cAA8H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC,EACDhE,KAAK,iBAAIzC,OAAA;gBAAGqG,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EAAE3D;cAAK;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eAENzG,OAAA;cAAKqG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACjBpG,OAAA;gBAAO4G,OAAO,EAAC,MAAM;gBAACP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAE/E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzG,OAAA;gBACImH,IAAI,EAAC,MAAM;gBACXhD,EAAE,EAAC,MAAM;gBACT2C,QAAQ,EAAGC,CAAC,IAAKrF,OAAO,CAACqF,CAAC,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAE;gBAC5CC,MAAM,EAAC,SAAS;gBAChBJ,QAAQ;gBACRZ,SAAS,EAAC;cAA8H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENzG,OAAA;cAAKqG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACjBpG,OAAA;gBAAO4G,OAAO,EAAC,MAAM;gBAACP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAE/E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzG,OAAA;gBACImH,IAAI,EAAC,MAAM;gBACXhD,EAAE,EAAC,MAAM;gBACT2C,QAAQ,EAAGC,CAAC,IAAKnF,OAAO,CAACmF,CAAC,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAE;gBAC5CC,MAAM,EAAC,SAAS;gBAChBJ,QAAQ;gBACRZ,SAAS,EAAC;cAA8H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENzG,OAAA;cAAKqG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACjBpG,OAAA;gBAAO4G,OAAO,EAAC,KAAK;gBAACP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAE9E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzG,OAAA;gBACImE,EAAE,EAAC,KAAK;gBACR0C,KAAK,EAAEhF,GAAI;gBACXiF,QAAQ,EAAGC,CAAC,IAAKjF,MAAM,CAACiF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACxCR,SAAS,EAAC,8HAA8H;gBACxIY,QAAQ;gBAAAb,QAAA,gBAERpG,OAAA;kBAAQ6G,KAAK,EAAC,EAAE;kBAAAT,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACnCxF,KAAK,CAACgD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACE,QAAQ,iBAC9BpE,OAAA;kBAAsB6G,KAAK,EAAE3C,IAAI,CAACE,QAAS;kBAAAgC,QAAA,EACtClC,IAAI,CAACE;gBAAQ,GADLF,IAAI,CAACC,EAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEZ,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAENzG,OAAA;cAAKqG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACjBpG,OAAA;gBAAO4G,OAAO,EAAC,SAAS;gBAACP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAElF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzG,OAAA;gBACImE,EAAE,EAAC,SAAS;gBACZ0C,KAAK,EAAE9E,OAAQ;gBACf+E,QAAQ,EAAGC,CAAC,IAAK/E,UAAU,CAAC+E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC5CR,SAAS,EAAC,8HAA8H;gBACxIY,QAAQ;gBAAAb,QAAA,gBAERpG,OAAA;kBAAQ6G,KAAK,EAAC,EAAE;kBAAAT,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvCxF,KAAK,CAACgD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACE,QAAQ,iBAC9BpE,OAAA;kBAAsB6G,KAAK,EAAE3C,IAAI,CAACE,QAAS;kBAAAgC,QAAA,EACtClC,IAAI,CAACE;gBAAQ,GADLF,IAAI,CAACC,EAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEZ,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAENzG,OAAA;cAAKqG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACjBpG,OAAA;gBAAO4G,OAAO,EAAC,UAAU;gBAACP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzG,OAAA;gBACImE,EAAE,EAAC,UAAU;gBACb0C,KAAK,EAAE5E,QAAS;gBAChB6E,QAAQ,EAAGC,CAAC,IAAK7E,WAAW,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC7CR,SAAS,EAAC,8HAA8H;gBACxIY,QAAQ;gBAAAb,QAAA,gBAERpG,OAAA;kBAAQ6G,KAAK,EAAC,EAAE;kBAAAT,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACzCxF,KAAK,CAACgD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACE,QAAQ,iBAC9BpE,OAAA;kBAAsB6G,KAAK,EAAE3C,IAAI,CAACE,QAAS;kBAAAgC,QAAA,EACtClC,IAAI,CAACE;gBAAQ,GADLF,IAAI,CAACC,EAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEZ,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAENzG,OAAA;cAAKqG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACjBpG,OAAA;gBAAOqG,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzG,OAAA;gBAAKqG,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,EAClCnD,UAAU,CAACgB,GAAG,CAAEd,GAAG,iBAChBnD,OAAA;kBAAiBqG,SAAS,EAAC,4CAA4C;kBAAAD,QAAA,gBACnEpG,OAAA;oBACImH,IAAI,EAAC,UAAU;oBACfG,OAAO,EAAEjF,OAAO,CAACgB,QAAQ,CAACF,GAAG,CAAE;oBAC/B2D,QAAQ,EAAEA,CAAA,KAAM5D,mBAAmB,CAACC,GAAG,CAAE;oBACzCkD,SAAS,EAAC;kBAA2D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACFzG,OAAA;oBAAMqG,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAEjD;kBAAG;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAP5CtD,GAAG;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQR,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACLpE,OAAO,CAACkF,MAAM,GAAG,CAAC,iBACfvH,OAAA;gBAAGqG,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,GAAC,YAC5B,EAAC/D,OAAO,CAACsD,IAAI,CAAC,IAAI,CAAC;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAENzG,OAAA;cAAKqG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACjBpG,OAAA;gBAAO4G,OAAO,EAAC,QAAQ;gBAACP,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAEjF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzG,OAAA;gBACImH,IAAI,EAAC,MAAM;gBACXhD,EAAE,EAAC,QAAQ;gBACX0C,KAAK,EAAE1E,MAAO;gBACd2E,QAAQ,EAAGC,CAAC,IAAK3E,SAAS,CAAC2E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC3CR,SAAS,EAAC;cAA8H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAELhE,KAAK,iBAAIzC,OAAA;YAAGqG,SAAS,EAAC,sBAAsB;YAAAD,QAAA,EAAE3D;UAAK;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzDzG,OAAA;YAAKqG,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC3BpG,OAAA;cACImH,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAC,6HAA6H;cAAAD,QAAA,gBAEvIpG,OAAA;gBAAMwH,KAAK,EAAC,0DAA0D;gBAAApB,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACvF1D,OAAO,GAAG,WAAW,GAAG,UAAU;YAAA;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,EAEL9D,cAAc,iBAAI3C,OAAA;YAAGqG,SAAS,EAAC,wBAAwB;YAAAD,QAAA,EAAEzD;UAAc;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC,gBAER,CAAC;AAEX,CAAC;AAAC3F,EAAA,CAxYIH,OAAO;EAAA,QACQf,WAAW,EACXC,WAAW;AAAA;AAAA4H,EAAA,GAF1B9G,OAAO;AA0Yb,eAAeA,OAAO;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}