{"version": 3, "file": "static/js/879.36040130.chunk.js", "mappings": "qQAGA,MAAMA,EAAUC,+DA+LhB,EA7LyBC,IAA6C,IAA5C,UAAEC,EAAS,WAAEC,EAAU,YAAEC,GAAaH,EAC5D,MAAOI,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,KAClDC,EAAeC,IAAoBF,EAAAA,EAAAA,UAAS,KAC5CG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,KAC5BK,EAAgBC,IAAqBN,EAAAA,EAAAA,UAAS,KAC9CO,EAAcC,IAAmBR,EAAAA,EAAAA,UAAS,OAGjDS,EAAAA,EAAAA,YAAU,KACN,MAAMC,EAASC,aAAaC,QAAQ,WAChCF,GACAF,EAAgBE,EACpB,GACD,KAEHD,EAAAA,EAAAA,YAAU,KACwBI,WAC1B,IAAKhB,EAAa,OAElB,MAAMiB,EAAQH,aAAaC,QAAQ,SACnC,GAAKE,EAKL,IACI,MAAMC,QAAiBC,MAAM,GAAGxB,mBAA0B,CACtDyB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,sBAIxB,IAAKC,EAASI,GACV,MAAM,IAAIC,MAAM,mCAAqCL,EAASM,YAGlE,MACMC,SADaP,EAASQ,QACL,kBACvB,IAAKC,MAAMC,QAAQH,GACf,MAAM,IAAIF,MAAM,2CAGpB,MAAMM,EAAWJ,EAAUK,MAAKC,GAAQA,EAAKC,KAAOhC,IACpD,IAAI6B,EAGA,MAAM,IAAIN,MAAM,iDAFhBrB,EAAoB2B,EAASI,KAIrC,CAAE,MAAO3B,GACLC,EAASD,EAAM4B,QACnB,MA/BI3B,EAAS,iCA+Bb,EAGJ4B,EAAuB,GACxB,CAACnC,IAuFJ,OAAKF,GAGDsC,EAAAA,EAAAA,KAAA,OACIC,UAAU,sGACVC,QAASA,IAAMvC,GAAW,GAAOwC,UAEjCC,EAAAA,EAAAA,MAAA,OACIH,UAAU,6DACVC,QAAUG,GAAMA,EAAEC,kBAAmBH,SAAA,EAErCC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yCAAwCE,SAAA,EACnDH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBE,SAAC,0BACtCH,EAAAA,EAAAA,KAAA,UACIC,UAAU,oCACVC,QAASA,IAAMvC,GAAW,GAAOwC,SACpC,YAIJjC,IAAS8B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcE,SAAEjC,IACxCE,IAAkB4B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBE,SAAE/B,KACpDgC,EAAAA,EAAAA,MAAA,QAAMG,SA1GG3B,UACjB4B,EAAMC,iBACN,MAAMC,EAAkB7C,EAAiB8C,OAGnCC,EAAYtC,EAElB,GAAKsC,EAAL,CAKA,GAAIrB,MAAMC,QAAQxB,GAAgB,CAM9B,GALmBA,EAAc6C,MAAKlB,GACZA,EAAKE,KAAKiB,cAAcH,SACrBD,EAAgBI,gBAG7B,CACZ3C,EAAS,8DACT,MAAM4C,EAAYC,YAAW,IAAM7C,EAAS,KAAK,KACjD,MAAO,IAAM8C,aAAaF,EAC9B,CACJ,CAEA5C,EAAS,IAET,IACI,MAAMU,EAAQH,aAAaC,QAAQ,SAE7BG,QAAiBC,MAAM,GAAGxB,oBAA0BK,IAAe,CACrEoB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,oBAEpBqC,KAAMC,KAAKC,UAAU,CACjBvB,KAAMa,EACNW,WAAYT,MAIpB,IAAK9B,EAASI,GACV,MAAM,IAAIC,MAAM,mCAAqCL,EAASM,YAGlE,MAAMkC,QAAexC,EAASQ,QAG9BiC,EAAAA,EAAAA,IAAa,CACTC,KAAM,UACNC,MAAO,WACPC,MAAY,OAANJ,QAAM,IAANA,OAAM,EAANA,EAAQxB,UAAW,wCAG7BhC,EAAoB,IAGpB,MAAM6D,QAAiC5C,MAAM,GAAGxB,mBAA0B,CACtEyB,OAAQ,MACRC,QAAS,CACL,cAAiB,UAAUJ,IAC3B,eAAgB,sBAIxB,IAAK8C,EAAyBzC,GAC1B,MAAM,IAAIC,MAAM,mCAAqCwC,EAAyBvC,YAGlF,MAAMwC,QAA6BD,EAAyBrC,OAC5DrB,EAAiB2D,EAAqBC,gBAAkB,IAGxDb,YAAW,KACPrD,GAAW,GACXU,EAAkB,GAAG,GACtB,IAEP,CAAE,MAAOH,IACLqD,EAAAA,EAAAA,IAAa,QACjB,CAvEA,MAFIpD,EAAS,yBAyEb,EAyBqCgC,SAAA,EACzBC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,OAAME,SAAA,EACjBH,EAAAA,EAAAA,KAAA,SAAO8B,QAAQ,OAAO7B,UAAU,aAAYE,SAAC,mBAC7CH,EAAAA,EAAAA,KAAA,SACIL,KAAK,OACLC,GAAG,OACHmC,MAAOlE,EACPmE,SAAW3B,GAAMvC,EAAoBuC,EAAE4B,OAAOF,OAC9C9B,UAAU,4BACViC,UAAQ,QAGhBlC,EAAAA,EAAAA,KAAA,UACIL,KAAK,SACLM,UAAU,gEAA+DE,SAC5E,iCArCM,IA0Cb,E,qCCrKd,MAAMgC,EAAc,uBAygBpB,EAtgB6BC,KAE3B,MAAOC,EAAeC,IAAoBvE,EAAAA,EAAAA,UAAS,CAAC,IAC7CwE,EAAuBC,IAA4BzE,EAAAA,EAAAA,UAAS,CAAC,IAC7D0E,EAAkBC,IAAuB3E,EAAAA,EAAAA,UAAS,KAClD4E,EAAaC,IAAkB7E,EAAAA,EAAAA,UAAS,KACxC8E,EAAcC,IAAmB/E,EAAAA,EAAAA,WAAS,IAC1CgF,EAAqBC,IAA0BjF,EAAAA,EAAAA,WAAS,IACxDH,EAAaqF,IAAkBlF,EAAAA,EAAAA,UAAS,OACxCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,OAC5BmF,EAAUC,IAAepF,EAAAA,EAAAA,UAAS,OAElCqF,EAAiBC,KADPC,EAAAA,EAAAA,OAC6BvF,EAAAA,EAAAA,WAAS,KAIhDwF,EAAYC,IAAiBzF,EAAAA,EAAAA,UAAS,eACtC0F,EAAeC,IAAoB3F,EAAAA,EAAAA,UAAS,SAC5C4F,EAASC,IAAc7F,EAAAA,EAAAA,UAAS,OAChC8F,EAAaC,IAAkB/F,EAAAA,EAAAA,UAAS,IAGvCgG,KAAMC,EAAS,WAAEC,EAAY/F,MAAOgG,IAAeC,EAAAA,EAAAA,KAA4B,CAAEC,QAASb,EAAYc,MAAOZ,EAAea,KAAMT,EAAaU,SAAUZ,EAASa,MAAO7B,KAE1K8B,GAAwBV,KAAMW,EAAWxG,MAAOyG,KAAoBC,EAAAA,EAAAA,QAEpEC,IAAsBC,EAAAA,EAAAA,OAGvBC,EAAoBC,IACxB,IAAIC,EAAIC,OAAOC,QAAQH,GAAiBI,QAAO,CAACC,EAAG5H,KAAoB,IAAjB6H,EAAKvD,GAAMtE,EAC/D,GAAqB,kBAAVsE,EACT,OAAOsD,EAAM,IAAIC,KAAOvD,IAE1B,GAAIxC,MAAMC,QAAQuC,GAAQ,CAExB,OAAOsD,EAAM,IAAIC,KADJvD,EAAMwD,KAAKC,GAAMA,EAAEzD,QAAO0D,KAAK,MAE9C,CACA,OAAOJ,CAAG,GACT,IAEHzC,EAAeqC,EAAE,EAGbS,EAAc3B,KAEE4B,EAAAA,EAAAA,IAAW5B,EADV,CAAC,KAAM,OAAQ,aAAc,aAAc,aAAc,UAAW,aAAc,UAAW,aAAc,eAEhIZ,EAAY,MACZL,GAAgB,EAAK,EAGjB8C,EAAchG,IAClBuD,EAAY,MACZF,EAAerD,GACfkD,GAAgB,EAAK,EAGjB+C,GAAgBjG,KACpBkG,EAAAA,EAAAA,IAAkB,CAACC,UAAWA,KAE1BlB,EAAmBjF,GACnBuD,EAAY,KAAK,GAChB,EAIP,IAAI6C,GAAe,EAEnB,MAAM,gBAAEC,KAAoBC,EAAAA,EAAAA,MAGrBC,GAASC,KAAcrI,EAAAA,EAAAA,WAAS,IAAM,CAC3C,CACI6B,GAAIoG,KACNnG,KAAM,SACNwG,MAAO,QACPpG,UAAW,aACXqG,KAAOC,IACLnG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,sCAAqCE,SAAA,EAElDH,EAAAA,EAAAA,KAAA,UACEC,UAAU,wLACVC,QAASA,IAAMiD,EAAYoD,GAAMpG,UAEjCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,kBAItC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChBxG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAM0F,EAAWW,EAAK3G,IAAIO,UAEnCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,mBAKxC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChBxG,EAAAA,EAAAA,KAAA,UACEC,UAAU,sLACVC,QAASA,IAAMwF,EAAWa,GAAMpG,UAEhCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,oBAKxC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChBxG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAM2F,GAAaU,EAAK3G,IAAIO,UAErCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,iBAM9D,CACIP,GAAIoG,KACNnG,KAAM,OACN4G,SAAUA,CAACC,EAAKC,KAAW9C,EAAc,GAAKF,EAAUgD,EAAQ,EAChEN,MAAO,OACPO,MAAM,GAER,CACIhH,GAAIoG,KACNnG,KAAM,uBACNgH,SAAU,OACVJ,SAAWC,GAAQA,EAAI7G,MAAQ,GAC/B+G,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACInH,GAAIoG,KACJnG,KAAM,aACN4G,SAAWC,IAAG,IAAAM,EAAAC,EAAA,MAAK,IAAc,QAAXD,EAAAN,EAAIQ,eAAO,IAAAF,OAAA,EAAXA,EAAaG,QAAS,OAAiB,QAAXF,EAAAP,EAAIQ,eAAO,IAAAD,OAAA,EAAXA,EAAaG,QAAS,IAAI,EAC5EP,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEhB,CACEnH,GAAIoG,KACJnG,KAAM,eACN4G,SAAWC,IAAQW,EAAAA,EAAAA,IAAkBX,EAAIY,YACzCT,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACInH,GAAIoG,KACJnG,KAAM,eACN4G,SAAWC,IAAQa,EAAAA,EAAAA,IAAmBb,EAAIY,YAC1CT,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACEnH,GAAIoG,KACJnG,KAAM,aACN4G,SAAWC,IAAG,IAAAc,EAAAC,EAAA,MAAK,IAAc,QAAXD,EAAAd,EAAIgB,eAAO,IAAAF,OAAA,EAAXA,EAAaL,QAAS,OAAiB,QAAXM,EAAAf,EAAIgB,eAAO,IAAAD,OAAA,EAAXA,EAAaL,QAAS,IAAI,EAC5EP,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEhB,CACEnH,GAAIoG,KACJnG,KAAM,eACN4G,SAAWC,IAAQW,EAAAA,EAAAA,IAAkBX,EAAIiB,YACzCd,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,GAEd,CACInH,GAAIoG,KACJnG,KAAM,eACN4G,SAAWC,IAAQa,EAAAA,EAAAA,IAAmBb,EAAIiB,YAC1Cd,SAAU,aACVD,MAAM,EACNE,UAAU,EACVC,YAAY,OAIlBvI,EAAAA,EAAAA,YAAU,KAER4H,IAAYwB,GAAgB,IACvBA,EAAYrC,KAAKsC,GACD,WAAbA,EAAIhI,KAEC,IACFgI,EACHvB,KAAOC,IACLnG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,sCAAqCE,SAAA,EAClDH,EAAAA,EAAAA,KAAA,UACEC,UAAU,wLACVC,QAASA,IAAMiD,EAAYoD,GAAMpG,UAEjCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,kBAEtC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAChBxG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAM0F,EAAWW,EAAK3G,IAAIO,UAEnCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,mBAIxC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAClBxG,EAAAA,EAAAA,KAAA,UACEC,UAAU,sLACVC,QAASA,IAAMwF,EAAWa,GAAMpG,UAEhCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,oBAItC,OAAf8F,SAAe,IAAfA,QAAe,EAAfA,GAAiBO,kBAClBxG,EAAAA,EAAAA,KAAA,UACEC,UAAU,oLACVC,QAASA,IAAM2F,GAAaU,EAAK3G,IAAIO,UAErCH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCE,SAAC,iBAOvD0H,MAET,GACD,CAAC5B,KAKJ,MAkBM6B,IAAWC,EAAAA,EAAAA,MAqDXC,IAA8BC,EAAAA,EAAAA,cAClCrJ,iBAKM,IAJJsJ,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACdxI,EAAIwI,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,QACPG,EAASH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACZI,EAASJ,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,SAGRK,EAAeN,EAAWrB,UAAY,QAE1C,IACEnE,EAAoB8F,GACpBxF,GAAuB,GAEvB,IAAI0B,EAAY,GAEhB,MAAM5F,QAAiB2F,EAAqB,CAAE9E,KAAMA,EAAKgB,OAAQ8H,OAAQD,EAAa7H,OAAQe,KAAM4G,EAAU3H,SAM9G,GAJI7B,EAASiF,OACXW,EAAY5F,EAASiF,MAGnBW,EAAU0D,OAAQ,CAEpB,GAAkB,eAAdG,EAMF,OALAjG,GAAkBoG,IAAI,IACjBA,EACH,CAACF,GAAe9D,MAGXA,EAGT,MAAMiE,EAAmBjE,EACtBa,KAAKgB,IACJ,GAAG2B,EAAWzB,SAAS,CACrB,IAAImC,EAAQV,EAAWzB,SAASF,GAEhC,OAAGqC,GACGrC,EAAKsC,OAAStC,EAAKsC,MAAQ,IAC7BD,GAAS,KAAKrC,EAAKsC,UAGd,CAAED,QAAO7G,MAAOwE,EAAKiC,KAGzB,IACP,KACCM,OAAOC,SAOZ,OALAzG,GAAkBoG,IAAI,IACjBA,EACH,CAACR,EAAWtI,KAAKoJ,EAAAA,EAAAA,IAAYL,OAGxBA,CACT,CACF,CAAE,MAAOzK,GACPC,EAASD,EAAM4B,QACjB,CAAC,QACCkD,GAAuB,EACzB,CACF,GACA,IAGF,OACEhD,EAAAA,EAAAA,KAAA,WAASC,UAAU,gEAA+DE,UAChFC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,gBAAeE,SAAA,EAE5BC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,iGAAgGE,SAAA,EAC7GH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BE,UAC3CH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sBAAqBE,SAAEgC,OAEvC/B,EAAAA,EAAAA,MAAA,OAAKH,UAAU,0CAAyCE,SAAA,EAEtDH,EAAAA,EAAAA,KAACiJ,EAAAA,GAAa,CAAC9C,QAASA,GAASC,WAAYA,MAG1CnC,GAAcD,GAAakF,SAASlF,EAAU6E,OAAS,IACxD7I,EAAAA,EAAAA,KAAAmJ,EAAAA,SAAA,CAAAhJ,UACEC,EAAAA,EAAAA,MAAA,UACEH,UAAU,oZACVC,QAvIMtB,UACpB,IAEE,MAAM0C,QAAewG,GACnBsB,EAAAA,IAAgBC,UAAUC,oBAAoBC,SAAS,CACrDnF,QAASb,EACTc,MAAOZ,EACPa,KAAMT,EACNU,UAAmB,OAATP,QAAS,IAATA,OAAS,EAATA,EAAW6E,QAAS,GAC9BrE,MAAO7B,KAET6G,SAEF,GAAW,OAANlI,QAAM,IAANA,IAAAA,EAAQuH,OAASvH,EAAOuH,MAAQ,EACnC,OAAO,EAGT,IAAIY,EAAK,EAET,IAAIC,EAAcpI,EAAOyC,KAAKwB,KAAKgB,IACjC,GAAIJ,GAAQiC,OAAQ,CAClB,IAAIuB,EAAM,CAAC,EAMX,OALAxD,GAAQyD,SAASnB,KACVA,EAAO7B,MAAQ6B,EAAOhC,WACzBkD,EAAIlB,EAAO5I,MAAwB,SAAhB4I,EAAO5I,KAAkB4J,IAAOhB,EAAOhC,SAASF,IAAS,GAC9E,IAEKoD,CACT,KAIF,MAAME,EAAYC,EAAAA,GAAWC,cAAcL,GACrCM,EAAWF,EAAAA,GAAWG,WAC5BH,EAAAA,GAAWI,kBAAkBF,EAAUH,EAAW,UAGlD,MAAMM,EAAcL,EAAAA,GAAWE,EAAU,CACvCI,SAAU,OACVzK,KAAM,UAEF0K,EAAO,IAAIC,KAAK,CAACH,GAAc,CAAExK,KAAM,8BAC7C4K,EAAAA,EAAAA,QAAOF,EAAM,GAAGlI,EAAYqI,QAAQ,KAAK,QAAQd,EAAYtB,cAC/D,CAAE,MAAOlK,GACPuM,QAAQvM,MAAM,4BAA6BA,EAC7C,GA0FqCiC,SAAA,CAEtB8D,IACCjE,EAAAA,EAAAA,KAAAmJ,EAAAA,SAAA,CAAAhJ,UACEH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDE,SAAC,yBAKxE8D,IACAjE,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yCAAwCE,SAAC,gBAGzD,oBACgB6D,EAAU6E,MAAM,SAKvC5C,GAAgBO,iBACfxG,EAAAA,EAAAA,KAAA,UACEC,UAAU,gYAEVC,QAASA,IAAMmD,GAAmB,GAAMlD,SACzC,mBAQPH,EAAAA,EAAAA,KAAC0K,EAAAA,GAAY,CACTvE,QAASA,GACT5D,sBAAuBA,EACvBC,yBAA0BA,EAC1BwF,4BAA6BA,GAC7B3F,cAAeA,EACfU,oBAAqBA,EACrBN,iBAAkBA,EAClBkI,UAlMQA,KAChB,GAAIzF,OAAO0F,KAAKrI,GAAuB6F,OAAQ,CAC7C,IAAIyC,EAAS,CAAC,EACd3F,OAAO0F,KAAKrI,GAAuBgD,KAAKD,IACI,kBAA/B/C,EAAsB+C,GAC/BuF,EAAOvF,GAAO,GAEduF,EAAOvF,GAAO,EAChB,IAEF9C,EAAyB,IAAKqI,IAC9B9F,EAAiB,IAAK8F,GACxB,CACA/G,EAAe,EAAE,EAsLTA,eAAgBA,EAChBiB,iBAAkBA,IAIrBb,IAAclE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcE,SAAEjC,IAE7C+F,IAAcjE,EAAAA,EAAAA,KAAC8K,EAAAA,EAAO,KAKvB9K,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mDAAkDE,UAC/DH,EAAAA,EAAAA,KAAC+K,EAAAA,GAAS,CACR5E,QAASA,GACTpC,MAAe,OAATC,QAAS,IAATA,OAAS,EAATA,EAAWD,OAAQ,GACzB9D,UAAU,8BACV+K,aAAW,EAEXC,kBAAgB,EAChBC,YAAU,EACVC,YAAU,EACVC,kBAAgB,EAChBC,kBAAmB1H,EACnB2H,qBAA8B,OAATtH,QAAS,IAATA,OAAS,EAATA,EAAW6E,QAAS,EACzC0C,aAAejH,IACTA,IAAST,GACXC,EAAeQ,EACjB,EAEFkH,oBAAsBC,IACjBA,IAAe9H,IAChBC,EAAW6H,GACX3H,EAAe,GACjB,EAEF4H,2BAA4B,CAC1BC,mBAAmB,EACnBC,sBAAuB,OAEzBC,YAAU,EACVC,OAAQ,SAACrD,GAAkC,IAA1BhF,EAAa0E,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,OAC1BjD,OAAO0F,KAAKnC,GAAQL,SACrB5E,EAAciF,EAAO5B,UAAY4B,EAAO5I,MAAQ,cAChD6D,EAAiBD,GAAiB,QAEtC,MAKHL,IACGpD,EAAAA,EAAAA,KAAC+L,EAAAA,EAAe,CACZrO,UAAW0F,EACXzF,WAAY0F,IAKnBR,IACC7C,EAAAA,EAAAA,KAACgM,EAAgB,CACftO,UAAWmF,EACXlF,WAAYmF,EACZlF,YAAaA,IAIhBsF,IAEClD,EAAAA,EAAAA,KAACiM,EAAAA,GAAS,CAAC1F,KAAMrD,EAAUC,YAAaA,EAAagD,QAASA,GAASP,WAAYA,EAAYC,aAAcA,SAIzG,EChhBd,EATqBqG,KAEjBlM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCE,UACrDH,EAAAA,EAAAA,KAACoC,EAAoB,K", "sources": ["pages/resource-type/EditResourceType.jsx", "pages/resource-type/ResourceTypeDataList.jsx", "dashboard/settings/ResourceType.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { alertMessage } from '../../common/coreui';\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst EditResourceType = ({ isVisible, setVisible, dataItemsId }) => {\r\n    const [resourceTypeName, setResourceTypeName] = useState('');\r\n    const [resourceTypes, setResourceTypes] = useState([]); // Store resource types fetched from API\r\n    const [error, setError] = useState('');\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n    const [loggedInUser, setLoggedInUser] = useState(null);\r\n\r\n    // Fetch logged-in user data (user_id)\r\n    useEffect(() => {\r\n        const userId = localStorage.getItem('user_id');\r\n        if (userId) {\r\n            setLoggedInUser(userId);\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        const fetchResourceTypeName = async () => {\r\n            if (!dataItemsId) return;\r\n\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('No authentication token found.');\r\n                return;\r\n            }\r\n\r\n            try {\r\n                const response = await fetch(`${API_URL}/resource_types`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!response.ok) {\r\n                    throw new Error('Failed to fetch resource types: ' + response.statusText);\r\n                }\r\n\r\n                const data = await response.json();\r\n                const typeArray = data['Resource Types']; // Adjusted to access resource types correctly\r\n                if (!Array.isArray(typeArray)) {\r\n                    throw new Error('Expected resource types to be an array.');\r\n                }\r\n\r\n                const typeData = typeArray.find(type => type.id === dataItemsId);\r\n                if (typeData) {\r\n                    setResourceTypeName(typeData.name);  // Set the name from the matching resource type\r\n                } else {\r\n                    throw new Error('Resource type not found. Please check the ID.');\r\n                }\r\n            } catch (error) {\r\n                setError(error.message);\r\n            }\r\n        };\r\n\r\n        fetchResourceTypeName();\r\n    }, [dataItemsId]);\r\n\r\n    // Update the Resource Type\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault();\r\n        const trimmedTypeName = resourceTypeName.trim();\r\n\r\n        \r\n        const updatedBy = loggedInUser;\r\n\r\n        if (!updatedBy) {\r\n            setError('User is not logged in.');\r\n            return;\r\n        }\r\n\r\n        if (Array.isArray(resourceTypes)) {\r\n            const typeExists = resourceTypes.some(type => {\r\n                const typeNameLower = type.name.toLowerCase().trim();\r\n                return typeNameLower === trimmedTypeName.toLowerCase();\r\n            });\r\n\r\n            if (typeExists) {\r\n                setError('Resource type already exists. Please add a different type.');\r\n                const timeoutId = setTimeout(() => setError(''), 3000);\r\n                return () => clearTimeout(timeoutId);\r\n            }\r\n        }\r\n\r\n        setError(''); // Clear any previous error\r\n\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n\r\n            const response = await fetch(`${API_URL}/resource_types/${dataItemsId}`, {\r\n                method: 'PUT',  // Use PUT for updating\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    name: trimmedTypeName,\r\n                    updated_by: updatedBy,\r\n                }),\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Failed to update resource type: ' + response.statusText);\r\n            }\r\n\r\n            const result = await response.json();\r\n            //setSuccessMessage(result.message || 'Resource type updated successfully!'); // Use message from API response\r\n\r\n            alertMessage({\r\n                icon: 'success',\r\n                title: 'Success!',\r\n                text: result?.message || 'Resource type updated successfully.',\r\n            });\r\n\r\n            setResourceTypeName(''); // Clear the input after success\r\n\r\n            // Optionally, refetch resource types after the update\r\n            const newResourceTypesResponse = await fetch(`${API_URL}/resource_types`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!newResourceTypesResponse.ok) {\r\n                throw new Error('Failed to fetch resource types: ' + newResourceTypesResponse.statusText);\r\n            }\r\n\r\n            const newResourceTypesData = await newResourceTypesResponse.json();\r\n            setResourceTypes(newResourceTypesData.resource_types || []);\r\n\r\n            // Close the modal after a short delay\r\n            setTimeout(() => {\r\n                setVisible(false);\r\n                setSuccessMessage(''); // Clear the success message\r\n            }, 1000);\r\n\r\n        } catch (error) {\r\n            alertMessage('error');\r\n        }\r\n    };\r\n\r\n    if (!isVisible) return null;\r\n\r\n    return (\r\n        <div\r\n            className=\"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50\"\r\n            onClick={() => setVisible(false)}\r\n        >\r\n            <div\r\n                className=\"relative bg-white rounded-lg shadow-lg max-w-md w-full p-5\"\r\n                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal\r\n            >\r\n                <div className=\"flex justify-between items-center mb-4\">\r\n                    <h3 className=\"text-lg font-semibold\">Update Resource Type</h3>\r\n                    <button\r\n                        className=\"text-gray-500 hover:text-gray-800\"\r\n                        onClick={() => setVisible(false)}\r\n                    >\r\n                        &times;\r\n                    </button>\r\n                </div>\r\n                {error && <div className=\"text-red-500\">{error}</div>}\r\n                {successMessage && <div className=\"text-green-500\">{successMessage}</div>}\r\n                <form onSubmit={handleSubmit}>\r\n                    <div className=\"mb-4\">\r\n                        <label htmlFor=\"name\" className=\"block mb-2\">Resource Type</label>\r\n                        <input\r\n                            type=\"text\"\r\n                            id=\"name\"\r\n                            value={resourceTypeName}\r\n                            onChange={(e) => setResourceTypeName(e.target.value)}\r\n                            className=\"border rounded w-full p-2\"\r\n                            required\r\n                        />\r\n                    </div>\r\n                    <button\r\n                        type=\"submit\"\r\n                        className=\"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2\"\r\n                    >\r\n                        Update Resource Type\r\n                    </button>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default EditResourceType;\r\n", "import React, { useState, useCallback, useEffect } from \"react\";\r\n\r\n// DataTable component for rendering tabular data with features like pagination and sorting\r\nimport DataTable from \"react-data-table-component\";\r\n\r\n// Loading spinner component to show while data is loading\r\nimport Loading from \"./../../common/Loading\";\r\n\r\nimport {confirmation<PERSON><PERSON>t, ManageColumns, SearchFilter, TableView} from './../../common/coreui';\r\n\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { defaultDateTimeFormat, removeKeys, sortByLabel } from \"./../../utils\";\r\n\r\n// Libraries for exporting data to Excel\r\nimport { saveAs } from \"file-saver\";\r\nimport * as XLSX from \"xlsx\";\r\nimport { resourceTypeApi, useDeleteResourceTypeMutation, useGetResourceTypeDataQuery, useLazyFetchDataOptionsForResourceTypeQuery } from \"./../../features/api\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport EditResourceType from \"./EditResourceType\";\r\nimport AddResourceType from \"./AddResourceType\";\r\nimport { useRoleBasedAccess } from \"./../../common/useRoleBasedAccess\";\r\nimport { DateTimeFormatDay, DateTimeFormatHour } from \"../../common/DateTimeFormatTable\";\r\n\r\n// API endpoint and configuration constants\r\nconst MODULE_NAME = \"Responsibility Level\";\r\n\r\n// Main component for listing Product Type List\r\nconst ResourceTypeDataList = () => {\r\n  // State variables for data items, filters, search text, modals, and loading status\r\n  const [filterOptions, setFilterOptions] = useState({});\r\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\r\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\r\n  const [queryString, setQueryString] = useState(\"\");\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\r\n  const [dataItemsId, setDataItemsId] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  const [viewData, setViewData] = useState(null);\r\n  const navigate = useNavigate();\r\n  const [addModalVisible, setAddModalVisible] = useState(false);\r\n\r\n  \r\n  // Sorting and pagination state\r\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\r\n  const [sortDirection, setSortDirection] = useState(\"desc\");\r\n  const [perPage, setPerPage] = useState(\"10\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  \r\n  const { data: dataItems, isFetching, error: fetchError } = useGetResourceTypeDataQuery({ sort_by: sortColumn, order: sortDirection, page: currentPage, per_page: perPage, query: queryString });\r\n\r\n  const [triggerFilterByFetch, { data: groupData, error: groupDataError }] = useLazyFetchDataOptionsForResourceTypeQuery();\r\n       \r\n  const [deleteResourceType] = useDeleteResourceTypeMutation();\r\n\r\n  // Build query parameters from selected filters\r\n  const buildQueryParams = (selectedFilters) => {\r\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\r\n      if (typeof value === \"string\") {\r\n        return acc + `&${key}=${value}`;\r\n      }\r\n      if (Array.isArray(value)) {\r\n        const vals = value.map((i) => i.value).join(\",\");\r\n        return acc + `&${key}=${vals}`;\r\n      }\r\n      return acc;\r\n    }, \"\")\r\n\r\n    setQueryString(q);\r\n  }\r\n\r\n  const handleCopy = (data) => {\r\n    const keysToRemove = [\"id\", \"team\", \"department\", \"updated_at\", \"updated_by\", \"updater\", \"created_at\", \"creator\", \"created_by\", \"updated_by\"];\r\n    const cleanedData = removeKeys(data, keysToRemove);\r\n    setViewData(null)\r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleEdit = (id) => {\r\n    setViewData(null)\r\n    setDataItemsId(id); \r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleDelete = (id) => {\r\n    confirmationAlert({onConfirm: () => \r\n      {        \r\n        deleteResourceType(id);\r\n        setViewData(null);\r\n      }});  \r\n  }\r\n \r\n\r\n  let columnSerial = 1;\r\n\r\n  const { rolePermissions } = useRoleBasedAccess();\r\n\r\n  // Define columns dynamically based on rolePermissions\r\n  const [columns, setColumns] = useState(() => [\r\n    {\r\n        id: columnSerial++,\r\n      name: \"Action\",\r\n      width: \"180px\",\r\n      className: \"bg-red-300\",\r\n      cell: (item) => (\r\n        <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n          {/* View Button */}\r\n          <button\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            onClick={() => setViewData(item)}\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n          </button>\r\n  \r\n          {/* Conditionally render Edit Button based on rolePermissions */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleEdit(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Copy Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleCopy(item)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Delete Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleDelete(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n      name: \"S.No\",\r\n      selector: (row, index) => (currentPage - 1) * perPage + index + 1,\r\n      width: \"80px\",\r\n      omit: false,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n      name: \"Resource Status Name\",\r\n      db_field: \"name\",\r\n      selector: (row) => row.name || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Created by\",\r\n        selector: (row) => `${row.creator?.fname || \"\"} ${row.creator?.lname || \"\"}`,\r\n        db_field: \"created_by\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Created Date\",\r\n      selector: (row) => DateTimeFormatDay(row.created_at),\r\n      db_field: \"created_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Created Time\",\r\n        selector: (row) => DateTimeFormatHour(row.created_at),\r\n        db_field: \"created_at\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: false,\r\n      },\r\n      {\r\n        id: columnSerial++,\r\n        name: \"Updated by\",\r\n        selector: (row) => `${row.updater?.fname || \"\"} ${row.updater?.lname || \"\"}`,\r\n        db_field: \"updated_by\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: true,\r\n      },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Updated Date\",\r\n      selector: (row) => DateTimeFormatDay(row.updated_at),\r\n      db_field: \"updated_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Updated Time\",\r\n        selector: (row) => DateTimeFormatHour(row.updated_at),\r\n        db_field: \"updated_at\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: false,\r\n    },\r\n  ]);\r\n  \r\n  useEffect(() => {\r\n    // Recalculate or update columns if rolePermissions change\r\n    setColumns((prevColumns) => [\r\n      ...prevColumns.map((col) => {\r\n        if (col.name === \"Action\") {\r\n          // Update the \"Action\" column dynamically\r\n          return {\r\n            ...col,\r\n            cell: (item) => (\r\n              <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => setViewData(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n                </button>\r\n                {rolePermissions?.hasManagerRole && (\r\n                  <button\r\n                    className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                    onClick={() => handleEdit(item.id)}\r\n                  >\r\n                    <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n                  </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleCopy(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n                </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleDelete(item.id)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n                </button>\r\n                )}\r\n              </div>\r\n            ),\r\n          };\r\n        }\r\n        return col;\r\n      }),\r\n    ]);\r\n  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes\r\n  \r\n  \r\n\r\n  // Resets the pagination and clear-all filter state\r\n  const resetPage = () => {\r\n    if (Object.keys(selectedFilterOptions).length) {\r\n      let newObj = {};\r\n      Object.keys(selectedFilterOptions).map((key) => {\r\n        if (typeof selectedFilterOptions[key] === \"string\") {\r\n          newObj[key] = \"\";\r\n        } else {\r\n          newObj[key] = [];\r\n        }\r\n      });\r\n      setSelectedFilterOptions({ ...newObj });\r\n      buildQueryParams({ ...newObj })\r\n    }\r\n    setCurrentPage(1);\r\n  };\r\n\r\n\r\n  // Export the fetched data into an Excel file\r\n  const dispatch = useDispatch();\r\n  const exportToExcel = async () => {\r\n    try {\r\n      // Fetch all data items for Excel export\r\n      const result = await dispatch(\r\n        resourceTypeApi.endpoints.getResourceTypeData.initiate({\r\n          sort_by: sortColumn,\r\n          order: sortDirection,\r\n          page: currentPage,\r\n          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues\r\n          query: queryString,\r\n        })\r\n      ).unwrap(); // Wait for the API response\r\n  \r\n      if (!result?.total || result.total < 1) {\r\n        return false;\r\n      }\r\n  \r\n      var sl = 1;\r\n  \r\n      let prepXlsData = result.data.map((item) => {\r\n        if (columns.length) {\r\n          let obj = {};\r\n          columns.forEach((column) => {\r\n            if (!column.omit && column.selector) {\r\n              obj[column.name] = column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\r\n            }\r\n          });\r\n          return obj;\r\n        }\r\n      });\r\n  \r\n      // Create a worksheet from the JSON data and append to a new workbook\r\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\r\n      const workbook = XLSX.utils.book_new();\r\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\r\n  \r\n      // Convert workbook to a buffer and create a Blob to trigger a file download\r\n      const excelBuffer = XLSX.write(workbook, {\r\n        bookType: \"xlsx\",\r\n        type: \"array\",\r\n      });\r\n      const blob = new Blob([excelBuffer], { type: \"application/octet-stream\" });\r\n      saveAs(blob, `${MODULE_NAME.replace(/ /g,\"_\")}_${prepXlsData.length}.xlsx`);\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n    }\r\n  };\r\n  \r\n\r\n  /**\r\n   * Fetch filter options from API for a specific field.\r\n   */\r\n  const fetchDataOptionsForFilterBy = useCallback(\r\n    async (\r\n      itemObject = {},\r\n      type = \"group\",\r\n      searching = \"\",\r\n      fieldType = \"select\"\r\n    ) => {\r\n\r\n      let groupByField = itemObject.db_field || \"title\";\r\n\r\n      try {\r\n        setShowFilterOption(groupByField);\r\n        setFilterOptionLoading(true);\r\n\r\n        var groupData = [];\r\n\r\n        const response = await triggerFilterByFetch({ type: type.trim(), column: groupByField.trim(), text: searching.trim() });\r\n        \r\n        if (response.data) {\r\n          groupData = response.data;\r\n        }\r\n\r\n        if (groupData.length) {\r\n\r\n          if (fieldType === \"searchable\") {\r\n            setFilterOptions((prev) => ({\r\n              ...prev,\r\n              [groupByField]: groupData,\r\n            }));\r\n\r\n            return groupData;\r\n          }\r\n\r\n          const optionsForFilter = groupData\r\n            .map((item) => {\r\n              if(itemObject.selector){\r\n                let label = itemObject.selector(item);\r\n\r\n                if(label){\r\n                  if (item.total && item.total > 1) {\r\n                    label += ` (${item.total})`;\r\n                  }\r\n\r\n                  return { label, value: item[groupByField] };\r\n                }\r\n\r\n              return null;\r\n              }\r\n            }).filter(Boolean);\r\n\r\n          setFilterOptions((prev) => ({\r\n            ...prev,\r\n            [itemObject.id]: sortByLabel(optionsForFilter),\r\n          }));\r\n\r\n          return optionsForFilter;\r\n        }\r\n      } catch (error) {\r\n        setError(error.message);\r\n      } finally {\r\n        setFilterOptionLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  return (\r\n    <section className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      <div className=\"mx-auto pb-6 \">\r\n        {/* Header section with title and action buttons */}\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4\">\r\n          <div className=\"w-4/12 md:w-10/12 text-start\">\r\n            <h2 className=\"text-2xl font-bold \">{MODULE_NAME}</h2>\r\n          </div>\r\n          <div className=\"w-8/12 flex items-end justify-end gap-1\">\r\n            {/* Manage Columns dropdown */}\r\n            <ManageColumns columns={columns} setColumns={setColumns} />\r\n            \r\n            {/* Export to Excel button, only shown if data exists */}\r\n            { !isFetching && dataItems && parseInt(dataItems.total) > 0 && (\r\n              <>\r\n                <button\r\n                  className=\"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n                  onClick={exportToExcel}\r\n                >\r\n                  {isFetching && (\r\n                    <>\r\n                      <span className=\"material-symbols-outlined animate-spin text-sm me-2\">\r\n                        progress_activity\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                  {!isFetching && (\r\n                    <span className=\"material-symbols-outlined text-sm me-2\">\r\n                    file_export\r\n                    </span>\r\n                  )}\r\n                  Export to Excel ({dataItems.total})\r\n                </button>\r\n              </>\r\n            )}\r\n            {/* Button to open modal for adding a new formation */}\r\n            {rolePermissions.hasManagerRole && (\r\n              <button\r\n                className=\" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n\r\n                onClick={() => setAddModalVisible(true)}\r\n              >\r\n                Add New\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filter fieldset for global search and field-specific filtering */}\r\n        <SearchFilter\r\n            columns={columns}\r\n            selectedFilterOptions={selectedFilterOptions}\r\n            setSelectedFilterOptions={setSelectedFilterOptions}\r\n            fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}\r\n            filterOptions={filterOptions}\r\n            filterOptionLoading={filterOptionLoading}\r\n            showFilterOption={showFilterOption}\r\n            resetPage={resetPage}\r\n            setCurrentPage={setCurrentPage}\r\n            buildQueryParams={buildQueryParams}\r\n        />\r\n\r\n        {/* Display error message if any error occurs */}\r\n        {fetchError && <div className=\"text-red-500\">{error}</div>}\r\n        {/* Show loading spinner when data is being fetched */}\r\n        {isFetching && <Loading />}\r\n\r\n        {/* If no data is available, display an alert message */}\r\n        \r\n        {/* Render the DataTable with the fetched data */}\r\n        <div className=\"border border-gray-200 p-0 pb-1 rounded-lg my-5 \">\r\n          <DataTable\r\n            columns={columns}\r\n            data={dataItems?.data || []}\r\n            className=\"p-0 scrollbar-horizontal-10\"\r\n            fixedHeader\r\n            \r\n            highlightOnHover\r\n            responsive\r\n            pagination\r\n            paginationServer\r\n            paginationPerPage={perPage}\r\n            paginationTotalRows={dataItems?.total || 0}\r\n            onChangePage={(page) => {\r\n              if (page !== currentPage) {\r\n                setCurrentPage(page);\r\n              }\r\n            }}\r\n            onChangeRowsPerPage={(newPerPage) => {\r\n              if(newPerPage !== perPage){\r\n                setPerPage(newPerPage);\r\n                setCurrentPage(1);\r\n              }\r\n            }}\r\n            paginationComponentOptions={{\r\n              selectAllRowsItem: true,\r\n              selectAllRowsItemText: \"ALL\",\r\n            }}\r\n            sortServer\r\n            onSort={(column, sortDirection=\"desc\") => {\r\n              if(Object.keys(column).length){\r\n                setSortColumn(column.db_field || column.name || \"created_at\");\r\n                setSortDirection(sortDirection || \"desc\");\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Add Modal */}\r\n        {addModalVisible && (\r\n            <AddResourceType\r\n                isVisible={addModalVisible}\r\n                setVisible={setAddModalVisible}\r\n            />\r\n        )}\r\n\r\n        {/* Conditionally render the Edit modal */}\r\n        {modalVisible && (\r\n          <EditResourceType\r\n            isVisible={modalVisible}\r\n            setVisible={setModalVisible}\r\n            dataItemsId={dataItemsId}\r\n          />\r\n        )}\r\n\r\n        {viewData && (\r\n          // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n          <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n        )}\r\n       \r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\n\r\nexport default ResourceTypeDataList;\r\n", "import React from 'react';\r\nimport ResourceTypeDataList from '../../pages/resource-type/ResourceTypeDataList';\r\n\r\n\r\nconst ResourceType = () => {\r\n  return (\r\n    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>\r\n      <ResourceTypeDataList />\r\n    </div>\r\n\r\n  );\r\n};\r\n\r\nexport default ResourceType;\r\n"], "names": ["API_URL", "process", "_ref", "isVisible", "setVisible", "dataItemsId", "resourceTypeName", "setResourceTypeName", "useState", "resourceTypes", "setResourceTypes", "error", "setError", "successMessage", "setSuccessMessage", "loggedInUser", "setLoggedInUser", "useEffect", "userId", "localStorage", "getItem", "async", "token", "response", "fetch", "method", "headers", "ok", "Error", "statusText", "typeArray", "json", "Array", "isArray", "typeData", "find", "type", "id", "name", "message", "fetchResourceTypeName", "_jsx", "className", "onClick", "children", "_jsxs", "e", "stopPropagation", "onSubmit", "event", "preventDefault", "trimmedTypeName", "trim", "updatedBy", "some", "toLowerCase", "timeoutId", "setTimeout", "clearTimeout", "body", "JSON", "stringify", "updated_by", "result", "alertMessage", "icon", "title", "text", "newResourceTypesResponse", "newResourceTypesData", "resource_types", "htmlFor", "value", "onChange", "target", "required", "MODULE_NAME", "ResourceTypeDataList", "filterOptions", "setFilterOptions", "selectedFilterOptions", "setSelectedFilterOptions", "showFilterOption", "setShowFilterOption", "queryString", "setQueryString", "modalVisible", "setModalVisible", "filterOptionLoading", "setFilterOptionLoading", "setDataItemsId", "viewData", "setViewData", "addModalVisible", "setAddModalVisible", "useNavigate", "sortColumn", "setSortColumn", "sortDirection", "setSortDirection", "perPage", "setPerPage", "currentPage", "setCurrentPage", "data", "dataItems", "isFetching", "fetchError", "useGetResourceTypeDataQuery", "sort_by", "order", "page", "per_page", "query", "triggerFilterByFetch", "groupData", "groupDataError", "useLazyFetchDataOptionsForResourceTypeQuery", "deleteResourceType", "useDeleteResourceTypeMutation", "buildQueryParams", "selectedFilters", "q", "Object", "entries", "reduce", "acc", "key", "map", "i", "join", "handleCopy", "<PERSON><PERSON><PERSON><PERSON>", "handleEdit", "handleDelete", "<PERSON><PERSON><PERSON><PERSON>", "onConfirm", "columnSerial", "rolePermissions", "useRoleBasedAccess", "columns", "setColumns", "width", "cell", "item", "hasManagerRole", "selector", "row", "index", "omit", "db_field", "sortable", "filterable", "_row$creator", "_row$creator2", "creator", "fname", "lname", "DateTimeFormatDay", "created_at", "DateTimeFormatHour", "_row$updater", "_row$updater2", "updater", "updated_at", "prevColumns", "col", "dispatch", "useDispatch", "fetchDataOptionsForFilterBy", "useCallback", "itemObject", "arguments", "length", "undefined", "searching", "fieldType", "groupByField", "column", "prev", "optionsForFilter", "label", "total", "filter", "Boolean", "sortByLabel", "ManageColumns", "parseInt", "_Fragment", "resourceTypeApi", "endpoints", "getResourceTypeData", "initiate", "unwrap", "sl", "prepXlsData", "obj", "for<PERSON>ach", "worksheet", "XLSX", "json_to_sheet", "workbook", "book_new", "book_append_sheet", "excelBuffer", "bookType", "blob", "Blob", "saveAs", "replace", "console", "SearchFilter", "resetPage", "keys", "newObj", "Loading", "DataTable", "fixedHeader", "highlightOnHover", "responsive", "pagination", "paginationServer", "paginationPerPage", "paginationTotalRows", "onChangePage", "onChangeRowsPerPage", "newPerPage", "paginationComponentOptions", "selectAllRowsItem", "selectAllRowsItemText", "sortServer", "onSort", "AddResourceType", "EditResourceType", "TableView", "ResourceType"], "sourceRoot": ""}