{"ast": null, "code": "import React from\"react\";import WeatherData from\"../pages/weatherAndTime/WeatherData\";import{API_URL}from\"../common/fetchData/apiConfig\";import{useNavigate}from\"react-router-dom\";import Loading from\"../common/Loading\";import WelcomeCard from\"./WelcomeCard\";import ClientTeamsSection from\"./ClientTeamsSection\";import ShiftSummarySection from\"./ShiftSummarySection\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const isTokenValid=()=>{const token=localStorage.getItem(\"token\");return token!==null&&token!==\"\";};const Dashboard=()=>{const[userData,setUserData]=React.useState(null);const[error,setError]=React.useState(null);const[filterOptionLoading,setFilterOptionLoading]=React.useState(false);const navigate=useNavigate();// Date/time strings (can be from backend/global; using static for now)\nconst[dateTimeStrings]=React.useState({english:\"Wednesday, 13 November, 2024, Late Autumn\",bengali:\"বুধবার, ২৮শে কার্তিক, ১৪৩১ বঙ্গাব্দ, হেমন্ত ঋতু\",hijri:\"Al-arbi 'aa', 11 Jumada 1446 Hijri, Awakhirul Kharif\"});React.useEffect(()=>{const token=localStorage.getItem(\"token\");if(!isTokenValid()){setError(\"No valid authentication token found.\");setFilterOptionLoading(false);navigate(\"/login\");return;}const user=localStorage.getItem(\"user\");if(user){setUserData(JSON.parse(user));return;}const fetchUserData=async()=>{setFilterOptionLoading(true);try{const response=await fetch(`${API_URL}/logged-users`,{method:\"GET\",headers:{Authorization:`Bearer ${token}`,\"Content-Type\":\"application/json\"}});if(!response.ok)throw new Error(\"Failed to fetch user data\");const data=await response.json();setUserData(data);}catch(err){setError(err.message);}finally{setFilterOptionLoading(false);}};fetchUserData();},[navigate]);if(filterOptionLoading)return/*#__PURE__*/_jsx(Loading,{});return/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-xl\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-12 gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-span-12 lg:col-span-7\",children:/*#__PURE__*/_jsx(WelcomeCard,{userData:userData,dateTimeStrings:dateTimeStrings})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-span-12 lg:col-span-5 flex flex-wrap border border-gray-300 dark:border-gray-600 rounded-2xl\",children:/*#__PURE__*/_jsx(WeatherData,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4\",children:/*#__PURE__*/_jsx(ClientTeamsSection,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4\",children:/*#__PURE__*/_jsx(ShiftSummarySection,{})}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 text-red-500\",children:error})]});};export default Dashboard;", "map": {"version": 3, "names": ["React", "WeatherData", "API_URL", "useNavigate", "Loading", "WelcomeCard", "ClientTeamsSection", "ShiftSummarySection", "jsx", "_jsx", "jsxs", "_jsxs", "isTokenValid", "token", "localStorage", "getItem", "Dashboard", "userData", "setUserData", "useState", "error", "setError", "filterOptionLoading", "setFilterOptionLoading", "navigate", "dateTimeStrings", "english", "bengali", "hijri", "useEffect", "user", "JSON", "parse", "fetchUserData", "response", "fetch", "method", "headers", "Authorization", "ok", "Error", "data", "json", "err", "message", "className", "children"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/Dashboard.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport WeatherData from \"../pages/weatherAndTime/WeatherData\";\r\nimport { API_URL } from \"../common/fetchData/apiConfig\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport Loading from \"../common/Loading\";\r\n\r\nimport WelcomeCard from \"./WelcomeCard\";\r\nimport ClientTeamsSection from \"./ClientTeamsSection\";\r\nimport ShiftSummarySection from \"./ShiftSummarySection\";\r\n\r\nconst isTokenValid = () => {\r\n  const token = localStorage.getItem(\"token\");\r\n  return token !== null && token !== \"\";\r\n};\r\n\r\nconst Dashboard = () => {\r\n  const [userData, setUserData] = React.useState(null);\r\n  const [error, setError] = React.useState(null);\r\n  const [filterOptionLoading, setFilterOptionLoading] = React.useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  // Date/time strings (can be from backend/global; using static for now)\r\n  const [dateTimeStrings] = React.useState({\r\n    english: \"Wednesday, 13 November, 2024, Late Autumn\",\r\n    bengali: \"বুধবার, ২৮শে কার্তিক, ১৪৩১ বঙ্গাব্দ, হেমন্ত ঋতু\",\r\n    hijri: \"Al-arbi 'aa', 11 Jumada 1446 Hijri, Awakhirul Kharif\",\r\n  });\r\n\r\n  React.useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n\r\n    if (!isTokenValid()) {\r\n      setError(\"No valid authentication token found.\");\r\n      setFilterOptionLoading(false);\r\n      navigate(\"/login\");\r\n      return;\r\n    }\r\n\r\n    const user = localStorage.getItem(\"user\");\r\n    if (user) {\r\n      setUserData(JSON.parse(user));\r\n      return;\r\n    }\r\n\r\n    const fetchUserData = async () => {\r\n      setFilterOptionLoading(true);\r\n      try {\r\n        const response = await fetch(`${API_URL}/logged-users`, {\r\n          method: \"GET\",\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n        });\r\n        if (!response.ok) throw new Error(\"Failed to fetch user data\");\r\n        const data = await response.json();\r\n        setUserData(data);\r\n      } catch (err) {\r\n        setError(err.message);\r\n      } finally {\r\n        setFilterOptionLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUserData();\r\n  }, [navigate]);\r\n\r\n  if (filterOptionLoading) return <Loading />;\r\n\r\n  return (\r\n    <div className=\"rounded-xl\">\r\n      {/* Top row: Welcome + Weather */}\r\n      <div className=\"grid grid-cols-12 gap-4\">\r\n        <div className=\"col-span-12 lg:col-span-7\">\r\n          <WelcomeCard userData={userData} dateTimeStrings={dateTimeStrings} />\r\n        </div>\r\n        <div className=\"col-span-12 lg:col-span-5 flex flex-wrap border border-gray-300 dark:border-gray-600 rounded-2xl\">\r\n          <WeatherData />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Clients/Teams grid */}\r\n      <div className=\"mt-4\">\r\n        <ClientTeamsSection />\r\n      </div>\r\n\r\n      {/* Shifts summary */}\r\n      <div className=\"mt-4\">\r\n        <ShiftSummarySection />\r\n      </div>\r\n\r\n      {error && <div className=\"mt-4 text-red-500\">{error}</div>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dashboard;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,WAAW,KAAM,qCAAqC,CAC7D,OAASC,OAAO,KAAQ,+BAA+B,CACvD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,OAAO,KAAM,mBAAmB,CAEvC,MAAO,CAAAC,WAAW,KAAM,eAAe,CACvC,MAAO,CAAAC,kBAAkB,KAAM,sBAAsB,CACrD,MAAO,CAAAC,mBAAmB,KAAM,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,MAAO,CAAAF,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,EAAE,CACvC,CAAC,CAED,KAAM,CAAAG,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGlB,KAAK,CAACmB,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGrB,KAAK,CAACmB,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACG,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGvB,KAAK,CAACmB,QAAQ,CAAC,KAAK,CAAC,CAC3E,KAAM,CAAAK,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACsB,eAAe,CAAC,CAAGzB,KAAK,CAACmB,QAAQ,CAAC,CACvCO,OAAO,CAAE,2CAA2C,CACpDC,OAAO,CAAE,iDAAiD,CAC1DC,KAAK,CAAE,sDACT,CAAC,CAAC,CAEF5B,KAAK,CAAC6B,SAAS,CAAC,IAAM,CACpB,KAAM,CAAAhB,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAE3C,GAAI,CAACH,YAAY,CAAC,CAAC,CAAE,CACnBS,QAAQ,CAAC,sCAAsC,CAAC,CAChDE,sBAAsB,CAAC,KAAK,CAAC,CAC7BC,QAAQ,CAAC,QAAQ,CAAC,CAClB,OACF,CAEA,KAAM,CAAAM,IAAI,CAAGhB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CACzC,GAAIe,IAAI,CAAE,CACRZ,WAAW,CAACa,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC,CAAC,CAC7B,OACF,CAEA,KAAM,CAAAG,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChCV,sBAAsB,CAAC,IAAI,CAAC,CAC5B,GAAI,CACF,KAAM,CAAAW,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGjC,OAAO,eAAe,CAAE,CACtDkC,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACPC,aAAa,CAAE,UAAUzB,KAAK,EAAE,CAChC,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CACF,GAAI,CAACqB,QAAQ,CAACK,EAAE,CAAE,KAAM,IAAI,CAAAC,KAAK,CAAC,2BAA2B,CAAC,CAC9D,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAP,QAAQ,CAACQ,IAAI,CAAC,CAAC,CAClCxB,WAAW,CAACuB,IAAI,CAAC,CACnB,CAAE,MAAOE,GAAG,CAAE,CACZtB,QAAQ,CAACsB,GAAG,CAACC,OAAO,CAAC,CACvB,CAAC,OAAS,CACRrB,sBAAsB,CAAC,KAAK,CAAC,CAC/B,CACF,CAAC,CAEDU,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,CAACT,QAAQ,CAAC,CAAC,CAEd,GAAIF,mBAAmB,CAAE,mBAAOb,IAAA,CAACL,OAAO,GAAE,CAAC,CAE3C,mBACEO,KAAA,QAAKkC,SAAS,CAAC,YAAY,CAAAC,QAAA,eAEzBnC,KAAA,QAAKkC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCrC,IAAA,QAAKoC,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxCrC,IAAA,CAACJ,WAAW,EAACY,QAAQ,CAAEA,QAAS,CAACQ,eAAe,CAAEA,eAAgB,CAAE,CAAC,CAClE,CAAC,cACNhB,IAAA,QAAKoC,SAAS,CAAC,kGAAkG,CAAAC,QAAA,cAC/GrC,IAAA,CAACR,WAAW,GAAE,CAAC,CACZ,CAAC,EACH,CAAC,cAGNQ,IAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBrC,IAAA,CAACH,kBAAkB,GAAE,CAAC,CACnB,CAAC,cAGNG,IAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBrC,IAAA,CAACF,mBAAmB,GAAE,CAAC,CACpB,CAAC,CAELa,KAAK,eAAIX,IAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAE1B,KAAK,CAAM,CAAC,EACvD,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}