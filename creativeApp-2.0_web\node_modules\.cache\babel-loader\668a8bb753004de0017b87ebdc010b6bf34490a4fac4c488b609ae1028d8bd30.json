{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useLocation,useNavigate}from'react-router-dom';import{alertMessage}from'../../common/coreui';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const API_URL=process.env.REACT_APP_BASE_API_URL;const isTokenValid=()=>{const token=localStorage.getItem('token');return token!==null;};const AddTeam=_ref=>{let{isVisible,setVisible}=_ref;const location=useLocation();const navigate=useNavigate();const[users,setUsers]=useState([]);const[departments,setDepartments]=useState([]);const[teams,setTeams]=useState([]);const[teamName,setTeamName]=useState('');const[icon,setIcon]=useState(null);const[logo,setLogo]=useState(null);const[poc,setPoc]=useState('');const[manager,setManager]=useState('');const[teamLead,setTeamLead]=useState('');const[launch,setLaunch]=useState('');const[workday,setWorkday]=useState([]);const[departmentId,setDepartmentId]=useState('');const[error,setError]=useState('');const[successMessage,setSuccessMessage]=useState('');const[loggedInUser,setLoggedInUser]=useState(null);const[loading,setLoading]=useState(true);// Days of the week for multi-select\nconst daysOfWeek=['Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'];// Handle workday selection\nconst handleWorkdayChange=day=>{setWorkday(prev=>{if(prev.includes(day)){return prev.filter(d=>d!==day);}else{return[...prev,day];}});};useEffect(()=>{const fetchData=async()=>{if(!isTokenValid()){setError('No authentication token found.');setLoading(false);return;}const token=localStorage.getItem('token');try{// Fetch Users\nconst usersResponse=await fetch(`${API_URL}/users`,{method:'GET',headers:{'Authorization':`Bearer ${token}`,'Content-Type':'application/json'}});if(!usersResponse.ok){throw new Error('Failed to fetch users');}const usersData=await usersResponse.json();setUsers(usersData.map(user=>({id:user.id,fullName:`${user.fname||''} ${user.lname||''}`.trim()})));// Fetch Departments\nconst departmentsResponse=await fetch(`${API_URL}/departments`,{method:'GET',headers:{'Authorization':`Bearer ${token}`,'Content-Type':'application/json'}});if(!departmentsResponse.ok){throw new Error('Failed to fetch departments');}const departmentsData=await departmentsResponse.json();setDepartments(departmentsData.departments);// Assuming the response has 'departments' array\n}catch(error){setError(error.message);}finally{setLoading(false);}};fetchData();},[]);// Fetch logged-in user data (user_id)\nuseEffect(()=>{const userId=localStorage.getItem('user_id');if(userId){setLoggedInUser(userId);}},[]);const handleSubmit=async event=>{event.preventDefault();// Prevent default form submission behavior\n// Get user_id from localStorage for 'created_by'\nconst createdBy=loggedInUser;if(!createdBy){setError('User is not logged in.');return;}const trimmedTeamName=teamName.trim();// Check if the team already exists\nconst teamExists=teams.some(team=>{const teamNameLower=team.name.toLowerCase().trim();return teamNameLower===trimmedTeamName.toLowerCase();});if(teamExists){setError('Team already exists. Please add a different team.');setTimeout(()=>setError(''),3000);return;// Exit if the team already exists\n}setError('');// Clear any previous error\ntry{const token=localStorage.getItem('token');if(!token){setError('Authentication token is missing.');return;// Exit if token is not available\n}const formData=new FormData();formData.append('name',trimmedTeamName);formData.append('icon',icon);formData.append('logo',logo);formData.append('poc',poc);formData.append('manager',manager);formData.append('team_lead',teamLead);formData.append('workday',JSON.stringify(workday));formData.append('launch',launch);formData.append('department_id',departmentId);formData.append('created_by',createdBy);const response=await fetch(`${API_URL}/teams`,{method:'POST',headers:{'Authorization':`Bearer ${token}`},body:formData});if(!response.ok){const errorData=await response.json();throw new Error(errorData.error||'Failed to save team: '+response.statusText);}const result=await response.json();//setSuccessMessage(`Team \"${result.name || trimmedTeamName}\" added successfully!`);\nalertMessage('success');setTeamName('');setIcon(null);setLogo(null);setPoc('');setManager('');setTeamLead('');setLaunch('');setWorkday([]);setDepartmentId('');// Refetch the teams list\nconst newTeamsResponse=await fetch(`${API_URL}/teams`,{method:'GET',headers:{'Authorization':`Bearer ${token}`,'Content-Type':'application/json'}});if(!newTeamsResponse.ok){throw new Error('Failed to fetch teams: '+newTeamsResponse.statusText);}const newTeamsData=await newTeamsResponse.json();setTeams(newTeamsData.teams);// Update the teams list\n}catch(error){setError(error.message||'Failed to add team.');console.error('Error adding team:',error);}};if(!isVisible)return null;return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-md w-full max-w-3xl relative overflow-y-auto h-[80vh] mt-10 scrollbar-vertical\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-4 bg-gray-100 p-4\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-xl text-left font-medium text-gray-800\",children:\"Add New Team\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setVisible(false),className:\"text-3xl text-gray-500 hover:text-gray-800\",children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"text-left p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"department\",className:\"block text-sm font-medium text-gray-700 pb-4\",children:\"Department\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"department\",value:departmentId,onChange:e=>setDepartmentId(e.target.value),className:\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\",required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Department\"}),departments.map(department=>/*#__PURE__*/_jsx(\"option\",{value:department.id,children:department.name},department.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"teamName\",className:\"block text-sm font-medium text-gray-700 pb-4\",children:\"Team Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"teamName\",value:teamName,onChange:e=>{setTeamName(e.target.value);if(error)setError('');// Clear error when user starts typing\n},required:true,className:\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"}),error&&/*#__PURE__*/_jsx(\"p\",{className:\"text-red-500 text-sm\",children:error})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"icon\",className:\"block text-sm font-medium text-gray-700 pb-4\",children:\"Icon\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"icon\",onChange:e=>setIcon(e.target.files[0]),accept:\"image/*\",required:true,className:\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"logo\",className:\"block text-sm font-medium text-gray-700 pb-4\",children:\"Logo\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"logo\",onChange:e=>setLogo(e.target.files[0]),accept:\"image/*\",required:true,className:\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"poc\",className:\"block text-sm font-medium text-gray-700 pb-4\",children:\"Point of Contact\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"poc\",value:poc,onChange:e=>setPoc(e.target.value),className:\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\",required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select POC\"}),users.map(user=>user.fullName&&/*#__PURE__*/_jsx(\"option\",{value:user.fullName,children:user.fullName},user.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"manager\",className:\"block text-sm font-medium text-gray-700 pb-4\",children:\"Manager\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"manager\",value:manager,onChange:e=>setManager(e.target.value),className:\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\",required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Manager\"}),users.map(user=>user.fullName&&/*#__PURE__*/_jsx(\"option\",{value:user.fullName,children:user.fullName},user.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"teamLead\",className:\"block text-sm font-medium text-gray-700 pb-4\",children:\"Team Lead\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"teamLead\",value:teamLead,onChange:e=>setTeamLead(e.target.value),className:\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\",required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Team Lead\"}),users.map(user=>user.fullName&&/*#__PURE__*/_jsx(\"option\",{value:user.fullName,children:user.fullName},user.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 pb-4\",children:\"Work Days\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 gap-2\",children:daysOfWeek.map(day=>/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center space-x-2 cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:workday.includes(day),onChange:()=>handleWorkdayChange(day),className:\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-700\",children:day})]},day))}),workday.length>0&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-gray-500 mt-2\",children:[\"Selected: \",workday.join(', ')]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"launch\",className:\"block text-sm font-medium text-gray-700 pb-4\",children:\"Launch Date\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",id:\"launch\",value:launch,onChange:e=>setLaunch(e.target.value),className:\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"})]})]}),error&&/*#__PURE__*/_jsx(\"p\",{className:\"text-red-500 text-sm\",children:error}),/*#__PURE__*/_jsx(\"div\",{className:\"text-left pt-6\",children:/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",className:\"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\",children:[/*#__PURE__*/_jsx(\"span\",{class:\"material-symbols-rounded text-white text-xl font-regular\",children:\"add_circle\"}),loading?'Adding...':'Add Team']})}),successMessage&&/*#__PURE__*/_jsx(\"p\",{className:\"text-green-500 text-sm\",children:successMessage})]})]})})});};export default AddTeam;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useLocation", "useNavigate", "alertMessage", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "API_URL", "process", "env", "REACT_APP_BASE_API_URL", "isTokenValid", "token", "localStorage", "getItem", "AddTeam", "_ref", "isVisible", "setVisible", "location", "navigate", "users", "setUsers", "departments", "setDepartments", "teams", "setTeams", "teamName", "setTeamName", "icon", "setIcon", "logo", "set<PERSON><PERSON>", "poc", "setPoc", "manager", "setManager", "teamLead", "setTeamLead", "launch", "setLaunch", "workday", "setWorkday", "departmentId", "setDepartmentId", "error", "setError", "successMessage", "setSuccessMessage", "loggedInUser", "setLoggedInUser", "loading", "setLoading", "daysOfWeek", "handleWorkdayChange", "day", "prev", "includes", "filter", "d", "fetchData", "usersResponse", "fetch", "method", "headers", "ok", "Error", "usersData", "json", "map", "user", "id", "fullName", "fname", "lname", "trim", "departmentsResponse", "departmentsData", "message", "userId", "handleSubmit", "event", "preventDefault", "created<PERSON>y", "trimmedTeamName", "teamExists", "some", "team", "teamNameLower", "name", "toLowerCase", "setTimeout", "formData", "FormData", "append", "JSON", "stringify", "response", "body", "errorData", "statusText", "result", "newTeamsResponse", "newTeamsData", "console", "children", "className", "onClick", "onSubmit", "htmlFor", "value", "onChange", "e", "target", "required", "department", "type", "files", "accept", "checked", "length", "join", "class"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/pages/team/AddTeam.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport { alertMessage } from '../../common/coreui';\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst isTokenValid = () => {\r\n    const token = localStorage.getItem('token');\r\n    return token !== null;\r\n};\r\n\r\nconst AddTeam = ({isVisible, setVisible}) => {\r\n    const location = useLocation();\r\n    const navigate = useNavigate();\r\n    const [users, setUsers] = useState([]);\r\n    const [departments, setDepartments] = useState([]);\r\n    const [teams, setTeams] = useState([]);\r\n    const [teamName, setTeamName] = useState('');\r\n    const [icon, setIcon] = useState(null);\r\n    const [logo, setLogo] = useState(null);\r\n    const [poc, setPoc] = useState('');\r\n    const [manager, setManager] = useState('');\r\n    const [teamLead, setTeamLead] = useState('');\r\n    const [launch, setLaunch] = useState('');\r\n    const [workday, setWorkday] = useState([]);\r\n    const [departmentId, setDepartmentId] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n    const [loggedInUser, setLoggedInUser] = useState(null);\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Days of the week for multi-select\r\n    const daysOfWeek = [\r\n        'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'\r\n    ];\r\n\r\n    // Handle workday selection\r\n    const handleWorkdayChange = (day) => {\r\n        setWorkday(prev => {\r\n            if (prev.includes(day)) {\r\n                return prev.filter(d => d !== day);\r\n            } else {\r\n                return [...prev, day];\r\n            }\r\n        });\r\n    };\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            if (!isTokenValid()) {\r\n                setError('No authentication token found.');\r\n                setLoading(false);\r\n                return;\r\n            }\r\n\r\n            const token = localStorage.getItem('token');\r\n\r\n            try {\r\n                // Fetch Users\r\n                const usersResponse = await fetch(`${API_URL}/users`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!usersResponse.ok) {\r\n                    throw new Error('Failed to fetch users');\r\n                }\r\n\r\n                const usersData = await usersResponse.json();\r\n                setUsers(usersData.map(user => ({\r\n                    id: user.id,\r\n                    fullName: `${user.fname || ''} ${user.lname || ''}`.trim(),\r\n                })));\r\n\r\n                // Fetch Departments\r\n                const departmentsResponse = await fetch(`${API_URL}/departments`, {\r\n                    method: 'GET',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                });\r\n\r\n                if (!departmentsResponse.ok) {\r\n                    throw new Error('Failed to fetch departments');\r\n                }\r\n\r\n                const departmentsData = await departmentsResponse.json();\r\n                setDepartments(departmentsData.departments); // Assuming the response has 'departments' array\r\n            } catch (error) {\r\n                setError(error.message);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, []);\r\n\r\n    // Fetch logged-in user data (user_id)\r\n    useEffect(() => {\r\n        const userId = localStorage.getItem('user_id');\r\n        if (userId) {\r\n            setLoggedInUser(userId);\r\n        }\r\n    }, []);\r\n\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault(); // Prevent default form submission behavior\r\n\r\n        // Get user_id from localStorage for 'created_by'\r\n        const createdBy = loggedInUser;\r\n\r\n        if (!createdBy) {\r\n            setError('User is not logged in.');\r\n            return;\r\n        }\r\n    \r\n        const trimmedTeamName = teamName.trim();\r\n    \r\n        // Check if the team already exists\r\n        const teamExists = teams.some((team) => {\r\n            const teamNameLower = team.name.toLowerCase().trim();\r\n            return teamNameLower === trimmedTeamName.toLowerCase();\r\n        });\r\n    \r\n        if (teamExists) {\r\n            setError('Team already exists. Please add a different team.');\r\n            setTimeout(() => setError(''), 3000);\r\n            return; // Exit if the team already exists\r\n        }\r\n    \r\n        setError(''); // Clear any previous error\r\n    \r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            if (!token) {\r\n                setError('Authentication token is missing.');\r\n                return; // Exit if token is not available\r\n            }\r\n    \r\n            const formData = new FormData();\r\n            formData.append('name', trimmedTeamName);\r\n            formData.append('icon', icon);\r\n            formData.append('logo', logo);\r\n            formData.append('poc', poc);\r\n            formData.append('manager', manager);\r\n            formData.append('team_lead', teamLead);\r\n            formData.append('workday', JSON.stringify(workday));\r\n            formData.append('launch', launch);\r\n            formData.append('department_id', departmentId);\r\n            formData.append('created_by', createdBy);\r\n\r\n    \r\n            const response = await fetch(`${API_URL}/teams`, {\r\n                method: 'POST',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                },\r\n                body: formData,\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                const errorData = await response.json();\r\n                throw new Error(errorData.error || 'Failed to save team: ' + response.statusText);\r\n            }\r\n    \r\n            const result = await response.json();\r\n            //setSuccessMessage(`Team \"${result.name || trimmedTeamName}\" added successfully!`);\r\n            alertMessage('success');\r\n\r\n            setTeamName('');\r\n            setIcon(null);\r\n            setLogo(null);\r\n            setPoc('');\r\n            setManager('');\r\n            setTeamLead('');\r\n            setLaunch('');\r\n            setWorkday([]);\r\n            setDepartmentId('');\r\n    \r\n            // Refetch the teams list\r\n            const newTeamsResponse = await fetch(`${API_URL}/teams`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n    \r\n            if (!newTeamsResponse.ok) {\r\n                throw new Error('Failed to fetch teams: ' + newTeamsResponse.statusText);\r\n            }\r\n    \r\n            const newTeamsData = await newTeamsResponse.json();\r\n            setTeams(newTeamsData.teams); // Update the teams list\r\n        } catch (error) {\r\n            setError(error.message || 'Failed to add team.');\r\n            console.error('Error adding team:', error);\r\n        }\r\n    };\r\n    \r\n\r\n    if (!isVisible) return null;\r\n\r\n    return (\r\n        <>\r\n            \r\n            <div className=\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\">\r\n                <div className=\"bg-white rounded-lg shadow-md w-full max-w-3xl relative overflow-y-auto h-[80vh] mt-10 scrollbar-vertical\">\r\n                    <div className=\"flex justify-between items-center mb-4 bg-gray-100 p-4\">\r\n                        <h4 className=\"text-xl text-left font-medium text-gray-800\">Add New Team</h4>\r\n                        <button onClick={() => setVisible(false)}\r\n                            className=\"text-3xl text-gray-500 hover:text-gray-800\"\r\n                            \r\n                        >\r\n                            &times;\r\n                        </button>\r\n                    </div>\r\n                    <form onSubmit={handleSubmit} className=\"text-left p-6\">\r\n                        <div className=''>                           \r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"department\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Department\r\n                                </label>\r\n                                <select\r\n                                    id=\"department\"\r\n                                    value={departmentId}\r\n                                    onChange={(e) => setDepartmentId(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select Department</option>\r\n                                    {departments.map((department) => (\r\n                                        <option key={department.id} value={department.id}>\r\n                                            {department.name}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                            </div>\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"teamName\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Team Name\r\n                                </label>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    id=\"teamName\"\r\n                                    value={teamName}\r\n                                    onChange={(e) => {\r\n                                        setTeamName(e.target.value);\r\n                                        if (error) setError(''); // Clear error when user starts typing\r\n                                    }}\r\n                                    required\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                />\r\n                                {error && <p className=\"text-red-500 text-sm\">{error}</p>}\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"icon\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Icon\r\n                                </label>\r\n                                <input\r\n                                    type=\"file\"\r\n                                    id=\"icon\"\r\n                                    onChange={(e) => setIcon(e.target.files[0])}\r\n                                    accept=\"image/*\"\r\n                                    required\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"logo\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Logo\r\n                                </label>\r\n                                <input\r\n                                    type=\"file\"\r\n                                    id=\"logo\"\r\n                                    onChange={(e) => setLogo(e.target.files[0])}\r\n                                    accept=\"image/*\"\r\n                                    required\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"poc\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Point of Contact\r\n                                </label>\r\n                                <select\r\n                                    id=\"poc\"\r\n                                    value={poc}\r\n                                    onChange={(e) => setPoc(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select POC</option>\r\n                                    {users.map((user) => user.fullName && (\r\n                                        <option key={user.id} value={user.fullName}>\r\n                                            {user.fullName}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"manager\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Manager\r\n                                </label>\r\n                                <select\r\n                                    id=\"manager\"\r\n                                    value={manager}\r\n                                    onChange={(e) => setManager(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select Manager</option>\r\n                                    {users.map((user) => user.fullName && (\r\n                                        <option key={user.id} value={user.fullName}>\r\n                                            {user.fullName}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"teamLead\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Team Lead\r\n                                </label>\r\n                                <select\r\n                                    id=\"teamLead\"\r\n                                    value={teamLead}\r\n                                    onChange={(e) => setTeamLead(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    required\r\n                                >\r\n                                    <option value=\"\">Select Team Lead</option>\r\n                                    {users.map((user) => user.fullName && (\r\n                                        <option key={user.id} value={user.fullName}>\r\n                                            {user.fullName}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Work Days\r\n                                </label>\r\n                                <div className=\"grid grid-cols-2 gap-2\">\r\n                                    {daysOfWeek.map((day) => (\r\n                                        <label key={day} className=\"flex items-center space-x-2 cursor-pointer\">\r\n                                            <input\r\n                                                type=\"checkbox\"\r\n                                                checked={workday.includes(day)}\r\n                                                onChange={() => handleWorkdayChange(day)}\r\n                                                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                                            />\r\n                                            <span className=\"text-sm text-gray-700\">{day}</span>\r\n                                        </label>\r\n                                    ))}\r\n                                </div>\r\n                                {workday.length > 0 && (\r\n                                    <p className=\"text-xs text-gray-500 mt-2\">\r\n                                        Selected: {workday.join(', ')}\r\n                                    </p>\r\n                                )}\r\n                            </div>\r\n\r\n                            <div className=\"mb-4\">\r\n                                <label htmlFor=\"launch\" className=\"block text-sm font-medium text-gray-700 pb-4\">\r\n                                    Launch Date\r\n                                </label>\r\n                                <input\r\n                                    type=\"date\"\r\n                                    id=\"launch\"\r\n                                    value={launch}\r\n                                    onChange={(e) => setLaunch(e.target.value)}\r\n                                    className=\"block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n\r\n                        {error && <p className=\"text-red-500 text-sm\">{error}</p>}\r\n                        \r\n                        <div className='text-left pt-6'>\r\n                            <button\r\n                                type=\"submit\"\r\n                                className=\"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\"\r\n                            >\r\n                                <span class=\"material-symbols-rounded text-white text-xl font-regular\">add_circle</span>\r\n                                {loading ? 'Adding...' : 'Add Team'}\r\n                            </button>\r\n                        </div>\r\n\r\n                        {successMessage && <p className=\"text-green-500 text-sm\">{successMessage}</p>}\r\n                    </form>\r\n                </div>\r\n            </div>\r\n          \r\n        </>\r\n    );\r\n};\r\n\r\nexport default AddTeam;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OAASC,YAAY,KAAQ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnD,KAAM,CAAAC,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,CAElD,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,MAAO,CAAAF,KAAK,GAAK,IAAI,CACzB,CAAC,CAED,KAAM,CAAAG,OAAO,CAAGC,IAAA,EAA6B,IAA5B,CAACC,SAAS,CAAEC,UAAU,CAAC,CAAAF,IAAA,CACpC,KAAM,CAAAG,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsB,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACsB,KAAK,CAAEC,QAAQ,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC0B,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC4B,KAAK,CAAEC,QAAQ,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC8B,QAAQ,CAAEC,WAAW,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACgC,IAAI,CAAEC,OAAO,CAAC,CAAGjC,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACkC,IAAI,CAAEC,OAAO,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACoC,GAAG,CAAEC,MAAM,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAClC,KAAM,CAACsC,OAAO,CAAEC,UAAU,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACwC,QAAQ,CAAEC,WAAW,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC0C,MAAM,CAAEC,SAAS,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAAC4C,OAAO,CAAEC,UAAU,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC8C,YAAY,CAAEC,eAAe,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACgD,KAAK,CAAEC,QAAQ,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACkD,cAAc,CAAEC,iBAAiB,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACoD,YAAY,CAAEC,eAAe,CAAC,CAAGrD,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACsD,OAAO,CAAEC,UAAU,CAAC,CAAGvD,QAAQ,CAAC,IAAI,CAAC,CAE5C;AACA,KAAM,CAAAwD,UAAU,CAAG,CACf,QAAQ,CAAE,SAAS,CAAE,WAAW,CAAE,UAAU,CAAE,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAC/E,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAIC,GAAG,EAAK,CACjCb,UAAU,CAACc,IAAI,EAAI,CACf,GAAIA,IAAI,CAACC,QAAQ,CAACF,GAAG,CAAC,CAAE,CACpB,MAAO,CAAAC,IAAI,CAACE,MAAM,CAACC,CAAC,EAAIA,CAAC,GAAKJ,GAAG,CAAC,CACtC,CAAC,IAAM,CACH,MAAO,CAAC,GAAGC,IAAI,CAAED,GAAG,CAAC,CACzB,CACJ,CAAC,CAAC,CACN,CAAC,CAED3D,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAgE,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC1B,GAAI,CAACjD,YAAY,CAAC,CAAC,CAAE,CACjBmC,QAAQ,CAAC,gCAAgC,CAAC,CAC1CM,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA,KAAM,CAAAxC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAE3C,GAAI,CACA;AACA,KAAM,CAAA+C,aAAa,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGvD,OAAO,QAAQ,CAAE,CAClDwD,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACL,eAAe,CAAE,UAAUpD,KAAK,EAAE,CAClC,cAAc,CAAE,kBACpB,CACJ,CAAC,CAAC,CAEF,GAAI,CAACiD,aAAa,CAACI,EAAE,CAAE,CACnB,KAAM,IAAI,CAAAC,KAAK,CAAC,uBAAuB,CAAC,CAC5C,CAEA,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAN,aAAa,CAACO,IAAI,CAAC,CAAC,CAC5C9C,QAAQ,CAAC6C,SAAS,CAACE,GAAG,CAACC,IAAI,GAAK,CAC5BC,EAAE,CAAED,IAAI,CAACC,EAAE,CACXC,QAAQ,CAAE,GAAGF,IAAI,CAACG,KAAK,EAAI,EAAE,IAAIH,IAAI,CAACI,KAAK,EAAI,EAAE,EAAE,CAACC,IAAI,CAAC,CAC7D,CAAC,CAAC,CAAC,CAAC,CAEJ;AACA,KAAM,CAAAC,mBAAmB,CAAG,KAAM,CAAAd,KAAK,CAAC,GAAGvD,OAAO,cAAc,CAAE,CAC9DwD,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACL,eAAe,CAAE,UAAUpD,KAAK,EAAE,CAClC,cAAc,CAAE,kBACpB,CACJ,CAAC,CAAC,CAEF,GAAI,CAACgE,mBAAmB,CAACX,EAAE,CAAE,CACzB,KAAM,IAAI,CAAAC,KAAK,CAAC,6BAA6B,CAAC,CAClD,CAEA,KAAM,CAAAW,eAAe,CAAG,KAAM,CAAAD,mBAAmB,CAACR,IAAI,CAAC,CAAC,CACxD5C,cAAc,CAACqD,eAAe,CAACtD,WAAW,CAAC,CAAE;AACjD,CAAE,MAAOsB,KAAK,CAAE,CACZC,QAAQ,CAACD,KAAK,CAACiC,OAAO,CAAC,CAC3B,CAAC,OAAS,CACN1B,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAEDQ,SAAS,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN;AACAhE,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmF,MAAM,CAAGlE,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,CAC9C,GAAIiE,MAAM,CAAE,CACR7B,eAAe,CAAC6B,MAAM,CAAC,CAC3B,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAC,KAAK,EAAK,CAClCA,KAAK,CAACC,cAAc,CAAC,CAAC,CAAE;AAExB;AACA,KAAM,CAAAC,SAAS,CAAGlC,YAAY,CAE9B,GAAI,CAACkC,SAAS,CAAE,CACZrC,QAAQ,CAAC,wBAAwB,CAAC,CAClC,OACJ,CAEA,KAAM,CAAAsC,eAAe,CAAGzD,QAAQ,CAACgD,IAAI,CAAC,CAAC,CAEvC;AACA,KAAM,CAAAU,UAAU,CAAG5D,KAAK,CAAC6D,IAAI,CAAEC,IAAI,EAAK,CACpC,KAAM,CAAAC,aAAa,CAAGD,IAAI,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACf,IAAI,CAAC,CAAC,CACpD,MAAO,CAAAa,aAAa,GAAKJ,eAAe,CAACM,WAAW,CAAC,CAAC,CAC1D,CAAC,CAAC,CAEF,GAAIL,UAAU,CAAE,CACZvC,QAAQ,CAAC,mDAAmD,CAAC,CAC7D6C,UAAU,CAAC,IAAM7C,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACpC,OAAQ;AACZ,CAEAA,QAAQ,CAAC,EAAE,CAAC,CAAE;AAEd,GAAI,CACA,KAAM,CAAAlC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAI,CAACF,KAAK,CAAE,CACRkC,QAAQ,CAAC,kCAAkC,CAAC,CAC5C,OAAQ;AACZ,CAEA,KAAM,CAAA8C,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEV,eAAe,CAAC,CACxCQ,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEjE,IAAI,CAAC,CAC7B+D,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAE/D,IAAI,CAAC,CAC7B6D,QAAQ,CAACE,MAAM,CAAC,KAAK,CAAE7D,GAAG,CAAC,CAC3B2D,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAE3D,OAAO,CAAC,CACnCyD,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAEzD,QAAQ,CAAC,CACtCuD,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAEC,IAAI,CAACC,SAAS,CAACvD,OAAO,CAAC,CAAC,CACnDmD,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAEvD,MAAM,CAAC,CACjCqD,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEnD,YAAY,CAAC,CAC9CiD,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAEX,SAAS,CAAC,CAGxC,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAAC,GAAGvD,OAAO,QAAQ,CAAE,CAC7CwD,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACL,eAAe,CAAE,UAAUpD,KAAK,EACpC,CAAC,CACDsF,IAAI,CAAEN,QACV,CAAC,CAAC,CAEF,GAAI,CAACK,QAAQ,CAAChC,EAAE,CAAE,CACd,KAAM,CAAAkC,SAAS,CAAG,KAAM,CAAAF,QAAQ,CAAC7B,IAAI,CAAC,CAAC,CACvC,KAAM,IAAI,CAAAF,KAAK,CAACiC,SAAS,CAACtD,KAAK,EAAI,uBAAuB,CAAGoD,QAAQ,CAACG,UAAU,CAAC,CACrF,CAEA,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAJ,QAAQ,CAAC7B,IAAI,CAAC,CAAC,CACpC;AACApE,YAAY,CAAC,SAAS,CAAC,CAEvB4B,WAAW,CAAC,EAAE,CAAC,CACfE,OAAO,CAAC,IAAI,CAAC,CACbE,OAAO,CAAC,IAAI,CAAC,CACbE,MAAM,CAAC,EAAE,CAAC,CACVE,UAAU,CAAC,EAAE,CAAC,CACdE,WAAW,CAAC,EAAE,CAAC,CACfE,SAAS,CAAC,EAAE,CAAC,CACbE,UAAU,CAAC,EAAE,CAAC,CACdE,eAAe,CAAC,EAAE,CAAC,CAEnB;AACA,KAAM,CAAA0D,gBAAgB,CAAG,KAAM,CAAAxC,KAAK,CAAC,GAAGvD,OAAO,QAAQ,CAAE,CACrDwD,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACL,eAAe,CAAE,UAAUpD,KAAK,EAAE,CAClC,cAAc,CAAE,kBACpB,CACJ,CAAC,CAAC,CAEF,GAAI,CAAC0F,gBAAgB,CAACrC,EAAE,CAAE,CACtB,KAAM,IAAI,CAAAC,KAAK,CAAC,yBAAyB,CAAGoC,gBAAgB,CAACF,UAAU,CAAC,CAC5E,CAEA,KAAM,CAAAG,YAAY,CAAG,KAAM,CAAAD,gBAAgB,CAAClC,IAAI,CAAC,CAAC,CAClD1C,QAAQ,CAAC6E,YAAY,CAAC9E,KAAK,CAAC,CAAE;AAClC,CAAE,MAAOoB,KAAK,CAAE,CACZC,QAAQ,CAACD,KAAK,CAACiC,OAAO,EAAI,qBAAqB,CAAC,CAChD0B,OAAO,CAAC3D,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC9C,CACJ,CAAC,CAGD,GAAI,CAAC5B,SAAS,CAAE,MAAO,KAAI,CAE3B,mBACIf,IAAA,CAAAI,SAAA,EAAAmG,QAAA,cAEIvG,IAAA,QAAKwG,SAAS,CAAC,kHAAkH,CAAAD,QAAA,cAC7HrG,KAAA,QAAKsG,SAAS,CAAC,2GAA2G,CAAAD,QAAA,eACtHrG,KAAA,QAAKsG,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACnEvG,IAAA,OAAIwG,SAAS,CAAC,6CAA6C,CAAAD,QAAA,CAAC,cAAY,CAAI,CAAC,cAC7EvG,IAAA,WAAQyG,OAAO,CAAEA,CAAA,GAAMzF,UAAU,CAAC,KAAK,CAAE,CACrCwF,SAAS,CAAC,4CAA4C,CAAAD,QAAA,CAEzD,MAED,CAAQ,CAAC,EACR,CAAC,cACNrG,KAAA,SAAMwG,QAAQ,CAAE5B,YAAa,CAAC0B,SAAS,CAAC,eAAe,CAAAD,QAAA,eACnDrG,KAAA,QAAKsG,SAAS,CAAC,EAAE,CAAAD,QAAA,eACbrG,KAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjBvG,IAAA,UAAO2G,OAAO,CAAC,YAAY,CAACH,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,YAErF,CAAO,CAAC,cACRrG,KAAA,WACImE,EAAE,CAAC,YAAY,CACfuC,KAAK,CAAEnE,YAAa,CACpBoE,QAAQ,CAAGC,CAAC,EAAKpE,eAAe,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDJ,SAAS,CAAC,8HAA8H,CACxIQ,QAAQ,MAAAT,QAAA,eAERvG,IAAA,WAAQ4G,KAAK,CAAC,EAAE,CAAAL,QAAA,CAAC,mBAAiB,CAAQ,CAAC,CAC1ClF,WAAW,CAAC8C,GAAG,CAAE8C,UAAU,eACxBjH,IAAA,WAA4B4G,KAAK,CAAEK,UAAU,CAAC5C,EAAG,CAAAkC,QAAA,CAC5CU,UAAU,CAAC1B,IAAI,EADP0B,UAAU,CAAC5C,EAEhB,CACX,CAAC,EACE,CAAC,EACR,CAAC,cACNnE,KAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjBvG,IAAA,UAAO2G,OAAO,CAAC,UAAU,CAACH,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,WAEnF,CAAO,CAAC,cACRvG,IAAA,UACIkH,IAAI,CAAC,MAAM,CACX7C,EAAE,CAAC,UAAU,CACbuC,KAAK,CAAEnF,QAAS,CAChBoF,QAAQ,CAAGC,CAAC,EAAK,CACbpF,WAAW,CAACoF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC3B,GAAIjE,KAAK,CAAEC,QAAQ,CAAC,EAAE,CAAC,CAAE;AAC7B,CAAE,CACFoE,QAAQ,MACRR,SAAS,CAAC,8HAA8H,CAC3I,CAAC,CACD7D,KAAK,eAAI3C,IAAA,MAAGwG,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAE5D,KAAK,CAAI,CAAC,EACxD,CAAC,cAENzC,KAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjBvG,IAAA,UAAO2G,OAAO,CAAC,MAAM,CAACH,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,MAE/E,CAAO,CAAC,cACRvG,IAAA,UACIkH,IAAI,CAAC,MAAM,CACX7C,EAAE,CAAC,MAAM,CACTwC,QAAQ,CAAGC,CAAC,EAAKlF,OAAO,CAACkF,CAAC,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAE,CAC5CC,MAAM,CAAC,SAAS,CAChBJ,QAAQ,MACRR,SAAS,CAAC,8HAA8H,CAC3I,CAAC,EACD,CAAC,cAENtG,KAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjBvG,IAAA,UAAO2G,OAAO,CAAC,MAAM,CAACH,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,MAE/E,CAAO,CAAC,cACRvG,IAAA,UACIkH,IAAI,CAAC,MAAM,CACX7C,EAAE,CAAC,MAAM,CACTwC,QAAQ,CAAGC,CAAC,EAAKhF,OAAO,CAACgF,CAAC,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAE,CAC5CC,MAAM,CAAC,SAAS,CAChBJ,QAAQ,MACRR,SAAS,CAAC,8HAA8H,CAC3I,CAAC,EACD,CAAC,cAENtG,KAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjBvG,IAAA,UAAO2G,OAAO,CAAC,KAAK,CAACH,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,kBAE9E,CAAO,CAAC,cACRrG,KAAA,WACImE,EAAE,CAAC,KAAK,CACRuC,KAAK,CAAE7E,GAAI,CACX8E,QAAQ,CAAGC,CAAC,EAAK9E,MAAM,CAAC8E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACxCJ,SAAS,CAAC,8HAA8H,CACxIQ,QAAQ,MAAAT,QAAA,eAERvG,IAAA,WAAQ4G,KAAK,CAAC,EAAE,CAAAL,QAAA,CAAC,YAAU,CAAQ,CAAC,CACnCpF,KAAK,CAACgD,GAAG,CAAEC,IAAI,EAAKA,IAAI,CAACE,QAAQ,eAC9BtE,IAAA,WAAsB4G,KAAK,CAAExC,IAAI,CAACE,QAAS,CAAAiC,QAAA,CACtCnC,IAAI,CAACE,QAAQ,EADLF,IAAI,CAACC,EAEV,CACX,CAAC,EACE,CAAC,EACR,CAAC,cAENnE,KAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjBvG,IAAA,UAAO2G,OAAO,CAAC,SAAS,CAACH,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,SAElF,CAAO,CAAC,cACRrG,KAAA,WACImE,EAAE,CAAC,SAAS,CACZuC,KAAK,CAAE3E,OAAQ,CACf4E,QAAQ,CAAGC,CAAC,EAAK5E,UAAU,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC5CJ,SAAS,CAAC,8HAA8H,CACxIQ,QAAQ,MAAAT,QAAA,eAERvG,IAAA,WAAQ4G,KAAK,CAAC,EAAE,CAAAL,QAAA,CAAC,gBAAc,CAAQ,CAAC,CACvCpF,KAAK,CAACgD,GAAG,CAAEC,IAAI,EAAKA,IAAI,CAACE,QAAQ,eAC9BtE,IAAA,WAAsB4G,KAAK,CAAExC,IAAI,CAACE,QAAS,CAAAiC,QAAA,CACtCnC,IAAI,CAACE,QAAQ,EADLF,IAAI,CAACC,EAEV,CACX,CAAC,EACE,CAAC,EACR,CAAC,cAENnE,KAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjBvG,IAAA,UAAO2G,OAAO,CAAC,UAAU,CAACH,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,WAEnF,CAAO,CAAC,cACRrG,KAAA,WACImE,EAAE,CAAC,UAAU,CACbuC,KAAK,CAAEzE,QAAS,CAChB0E,QAAQ,CAAGC,CAAC,EAAK1E,WAAW,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7CJ,SAAS,CAAC,8HAA8H,CACxIQ,QAAQ,MAAAT,QAAA,eAERvG,IAAA,WAAQ4G,KAAK,CAAC,EAAE,CAAAL,QAAA,CAAC,kBAAgB,CAAQ,CAAC,CACzCpF,KAAK,CAACgD,GAAG,CAAEC,IAAI,EAAKA,IAAI,CAACE,QAAQ,eAC9BtE,IAAA,WAAsB4G,KAAK,CAAExC,IAAI,CAACE,QAAS,CAAAiC,QAAA,CACtCnC,IAAI,CAACE,QAAQ,EADLF,IAAI,CAACC,EAEV,CACX,CAAC,EACE,CAAC,EACR,CAAC,cAENnE,KAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjBvG,IAAA,UAAOwG,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,WAEhE,CAAO,CAAC,cACRvG,IAAA,QAAKwG,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAClCpD,UAAU,CAACgB,GAAG,CAAEd,GAAG,eAChBnD,KAAA,UAAiBsG,SAAS,CAAC,4CAA4C,CAAAD,QAAA,eACnEvG,IAAA,UACIkH,IAAI,CAAC,UAAU,CACfG,OAAO,CAAE9E,OAAO,CAACgB,QAAQ,CAACF,GAAG,CAAE,CAC/BwD,QAAQ,CAAEA,CAAA,GAAMzD,mBAAmB,CAACC,GAAG,CAAE,CACzCmD,SAAS,CAAC,2DAA2D,CACxE,CAAC,cACFxG,IAAA,SAAMwG,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAElD,GAAG,CAAO,CAAC,GAP5CA,GAQL,CACV,CAAC,CACD,CAAC,CACLd,OAAO,CAAC+E,MAAM,CAAG,CAAC,eACfpH,KAAA,MAAGsG,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAC,YAC5B,CAAChE,OAAO,CAACgF,IAAI,CAAC,IAAI,CAAC,EAC9B,CACN,EACA,CAAC,cAENrH,KAAA,QAAKsG,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjBvG,IAAA,UAAO2G,OAAO,CAAC,QAAQ,CAACH,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,aAEjF,CAAO,CAAC,cACRvG,IAAA,UACIkH,IAAI,CAAC,MAAM,CACX7C,EAAE,CAAC,QAAQ,CACXuC,KAAK,CAAEvE,MAAO,CACdwE,QAAQ,CAAGC,CAAC,EAAKxE,SAAS,CAACwE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC3CJ,SAAS,CAAC,8HAA8H,CAC3I,CAAC,EACD,CAAC,EACL,CAAC,CAEL7D,KAAK,eAAI3C,IAAA,MAAGwG,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAE5D,KAAK,CAAI,CAAC,cAEzD3C,IAAA,QAAKwG,SAAS,CAAC,gBAAgB,CAAAD,QAAA,cAC3BrG,KAAA,WACIgH,IAAI,CAAC,QAAQ,CACbV,SAAS,CAAC,6HAA6H,CAAAD,QAAA,eAEvIvG,IAAA,SAAMwH,KAAK,CAAC,0DAA0D,CAAAjB,QAAA,CAAC,YAAU,CAAM,CAAC,CACvFtD,OAAO,CAAG,WAAW,CAAG,UAAU,EAC/B,CAAC,CACR,CAAC,CAELJ,cAAc,eAAI7C,IAAA,MAAGwG,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAE1D,cAAc,CAAI,CAAC,EAC3E,CAAC,EACN,CAAC,CACL,CAAC,CAER,CAAC,CAEX,CAAC,CAED,cAAe,CAAAhC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}