{"__meta": {"id": "X863ebdd838a207f2c314a9fbdc58c2ff", "datetime": "2025-08-11 16:13:27", "utime": **********.381655, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.0.30", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754907206.700138, "end": **********.381669, "duration": 0.6815309524536133, "duration_str": "682ms", "measures": [{"label": "Booting", "start": 1754907206.700138, "relative_start": 0, "end": **********.34142, "relative_end": **********.34142, "duration": 0.6412818431854248, "duration_str": "641ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.341433, "relative_start": 0.6412949562072754, "end": **********.381671, "relative_end": 1.9073486328125e-06, "duration": 0.0402379035949707, "duration_str": "40.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21071184, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "welcome", "param_count": null, "params": [], "start": **********.373367, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\resources\\views/welcome.blade.phpwelcome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fresources%2Fviews%2Fwelcome.blade.php&line=1", "ajax": false, "filename": "welcome.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#1554\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#255 …}\n  file: \"C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\routes\\web.php\"\n  line: \"16 to 18\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Froutes%2Fweb.php&line=16\" onclick=\"\">routes/web.php:16-18</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "vh3HGIRzxNnbtx40hsz73GqdcLKkFGE4xFpGEvoH", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-663914614 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-663914614\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-504818767 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-504818767\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1508772423 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1508772423\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1813287459 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813287459\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-784572838 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-784572838\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1654352849 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 10:13:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"459 characters\">XSRF-TOKEN=eyJpdiI6Iis4NTNmM2ZzUklFNll5M1VyVGJuSUE9PSIsInZhbHVlIjoiblE4dWtzeE1sOVljbmlNRy8wWkRhSTA4aUVWeXloRDYxQ3NwY1FPekEzUU10R3dvN1JWbks3OVR3MnJ5clBqRjdaWERQTGpmbVNrREJTS1pIVFMxcEUzWDM4aHM2amJ1STEvMHhQYUltRitBM3p6clJPRE5oWk1Ld09MWjkyZHkiLCJtYWMiOiI0MzRkYjRjM2ZkNjY4YzViN2ZiNmFjOTZhYzlhN2U3Y2ViY2M1NTJiMWEyYzY5OTg1NmNkOGE5YTlmYTU0OTZjIiwidGFnIjoiIn0%3D; expires=Mon, 11-Aug-2025 12:13:27 GMT; Max-Age=7200; path=/; domain=.creativeapp.sebpo.net; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"478 characters\">creativeapp_session=eyJpdiI6IitkZjV4bFVLbWVBYTF0Q29WWkZpY2c9PSIsInZhbHVlIjoiUExWOEs2U1A4bUpkQktPNVplZ0JBSVQrN1pMM29RQ1liUjVyc2RRaVhjRDFYTWgva0lxdWF4ZjV5V3BaSVNTT3FVcFU3QzBkYWhCN2FMNFJrem54L212MmJ6QjV3YndGUmlpQnFlTGowQmtSYWlqMk1kSkR2Rm9ialE3L2Eva1oiLCJtYWMiOiI1ZmMxNGNjZGU5MDkxN2FhMmU3YmUyZjQ2YzNjOTM1NmZlNWNjZGNmNjhhNmQxNDI1ZTZjYTAyZjNmYWU0YzJjIiwidGFnIjoiIn0%3D; expires=Mon, 11-Aug-2025 12:13:27 GMT; Max-Age=7200; path=/; domain=.creativeapp.sebpo.net; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"431 characters\">XSRF-TOKEN=eyJpdiI6Iis4NTNmM2ZzUklFNll5M1VyVGJuSUE9PSIsInZhbHVlIjoiblE4dWtzeE1sOVljbmlNRy8wWkRhSTA4aUVWeXloRDYxQ3NwY1FPekEzUU10R3dvN1JWbks3OVR3MnJ5clBqRjdaWERQTGpmbVNrREJTS1pIVFMxcEUzWDM4aHM2amJ1STEvMHhQYUltRitBM3p6clJPRE5oWk1Ld09MWjkyZHkiLCJtYWMiOiI0MzRkYjRjM2ZkNjY4YzViN2ZiNmFjOTZhYzlhN2U3Y2ViY2M1NTJiMWEyYzY5OTg1NmNkOGE5YTlmYTU0OTZjIiwidGFnIjoiIn0%3D; expires=Mon, 11-Aug-2025 12:13:27 GMT; domain=.creativeapp.sebpo.net; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"450 characters\">creativeapp_session=eyJpdiI6IitkZjV4bFVLbWVBYTF0Q29WWkZpY2c9PSIsInZhbHVlIjoiUExWOEs2U1A4bUpkQktPNVplZ0JBSVQrN1pMM29RQ1liUjVyc2RRaVhjRDFYTWgva0lxdWF4ZjV5V3BaSVNTT3FVcFU3QzBkYWhCN2FMNFJrem54L212MmJ6QjV3YndGUmlpQnFlTGowQmtSYWlqMk1kSkR2Rm9ialE3L2Eva1oiLCJtYWMiOiI1ZmMxNGNjZGU5MDkxN2FhMmU3YmUyZjQ2YzNjOTM1NmZlNWNjZGNmNjhhNmQxNDI1ZTZjYTAyZjNmYWU0YzJjIiwidGFnIjoiIn0%3D; expires=Mon, 11-Aug-2025 12:13:27 GMT; domain=.creativeapp.sebpo.net; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654352849\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1623618926 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vh3HGIRzxNnbtx40hsz73GqdcLKkFGE4xFpGEvoH</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623618926\", {\"maxDepth\":0})</script>\n"}}