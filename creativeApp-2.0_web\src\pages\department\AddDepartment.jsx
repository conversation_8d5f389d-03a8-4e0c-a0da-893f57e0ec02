import React, { useEffect, useState } from 'react';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddDepartment = ({isVisible, setVisible}) => {
    const [departmentName, setDepartmentName] = useState('');
    const [manager, setManager] = useState('');
    const [launchDate, setLaunchDate] = useState('');
    const [users, setUsers] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);
    
    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchInitialData = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
            const token = localStorage.getItem('token');
            try {
                // Fetch departments for checking duplicates
                const depRes = await fetch(`${API_URL}/departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
                if (!depRes.ok) throw new Error('Network response was not ok: ' + depRes.statusText);
                const depData = await depRes.json();
                setDepartments(depData.departments || depData.data || []);

                // Fetch users for manager dropdown
                const userRes = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
                if (!userRes.ok) throw new Error('Network response was not ok: ' + userRes.statusText);
                const userData = await userRes.json();
                setUsers(userData.users || userData.data || []);
            } catch (error) {
                setError(error.message);
            }
        };
        fetchInitialData();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();

        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }

        const trimmedDepartmentName = departmentName.trim();

        if (!trimmedDepartmentName) {
            setError('Department name is required.');
            return;
        }

        // Check if the department already exists
        const departmentExists = departments.some((department) => {
            const departmentNameLower = department.name.toLowerCase().trim();
            return departmentNameLower === trimmedDepartmentName.toLowerCase();
        });

        if (departmentExists) {
            setError('Department already exists. Please add a different department.');
            setTimeout(() => setError(''), 3000);
            return;
        }

        setError('');

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            // Send only the fields that exist in the database
            const response = await fetch(`${API_URL}/departments`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedDepartmentName,
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to save department');
            }

            const result = await response.json();
            setSuccessMessage(result?.message || 'Department added successfully.');

            // Clear form fields
            setDepartmentName('');
            setManager('');
            setLaunchDate('');

            // Close modal after success
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 1500);

        } catch (error) {
            setError(error.message || 'Failed to add department.');
        }
    };
    

    // Only show modal if visible
    if (!isVisible) return null;

    return (
        <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
            <div className="bg-white rounded-lg shadow-md w-full max-w-3xl relative overflow-y-auto h-[80vh] mt-10 scrollbar-vertical">
                <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                    <h4 className="text-xl text-left font-medium text-gray-800">Add New Department</h4>
                    <button
                        onClick={() => setVisible(false)}
                        className="text-3xl text-gray-500 hover:text-gray-800"
                    >
                        &times;
                    </button>
                </div>
                <form onSubmit={handleSubmit} className="text-left p-6">
                    <div className="mb-4">
                        <label htmlFor="departmentName" className="block text-sm font-medium text-gray-700 pb-4">
                            Department Name
                        </label>
                        <input
                            type="text"
                            id="departmentName"
                            value={departmentName}
                            onChange={(e) => setDepartmentName(e.target.value)}
                            required
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            placeholder="Enter department name"
                        />
                    </div>

                    <div className="mb-4">
                        <label htmlFor="manager" className="block text-sm font-medium text-gray-700 pb-4">
                            Manager
                        </label>
                        <select
                            id="manager"
                            value={manager}
                            onChange={(e) => setManager(e.target.value)}
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                        >
                            <option value="">Select Manager</option>
                            {users.map((user) => (
                                <option key={user.id} value={user.id}>
                                    {user.fname} {user.lname}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="mb-4">
                        <label htmlFor="launchDate" className="block text-sm font-medium text-gray-700 pb-4">
                            Launch Date
                        </label>
                        <input
                            type="date"
                            id="launchDate"
                            value={launchDate}
                            onChange={(e) => setLaunchDate(e.target.value)}
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                        />
                    </div>

                    {error && <p className="text-red-500 text-sm mb-4">{error}</p>}
                    {successMessage && <p className="text-green-500 text-sm mb-4">{successMessage}</p>}

                    <div className="py-4">
                        <button
                            type="submit"
                            className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                        >
                            Add Department
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default AddDepartment;
