import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddDepartment = ({isVisible, setVisible}) => {
    const [departmentId, setDepartmentId] = useState('');
    // Removed unused location and navigate
    const [departments, setDepartments] = useState([]);
    const [departmentName, setDepartmentName] = useState('');
    const [pointOfContact, setPointOfContact] = useState('');
    const [manager, setManager] = useState('');
    const [teamLead, setTeamLead] = useState('');
    const [users, setUsers] = useState([]);
    const [teams, setTeams] = useState([]);
    const [workDay, setWorkDay] = useState('');
    const [launchDate, setLaunchDate] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState(''); // Will use for feedback
    const [loggedInUser, setLoggedInUser] = useState(null);
    
    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchInitialData = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
            const token = localStorage.getItem('token');
            try {
                // Fetch departments
                const depRes = await fetch(`${API_URL}/departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
                if (!depRes.ok) throw new Error('Network response was not ok: ' + depRes.statusText);
                const depData = await depRes.json();
                setDepartments(depData.departments);
                console.log('Fetched departments:', depData);

                // Fetch users
                const userRes = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
                if (!userRes.ok) throw new Error('Network response was not ok: ' + userRes.statusText);
                const userData = await userRes.json();
                setUsers(userData.users || userData.data || []);
                console.log('Fetched users:', userData);

                // Fetch teams
                const teamRes = await fetch(`${API_URL}/teams`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
                if (!teamRes.ok) throw new Error('Network response was not ok: ' + teamRes.statusText);
                const teamData = await teamRes.json();
                setTeams(teamData.teams || teamData.data || []);
                console.log('Fetched teams:', teamData);
            } catch (error) {
                setError(error.message);
            }
        };
        fetchInitialData();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault(); // Prevent default form submission behavior

        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }
    
        const trimmedDepartmentName = departmentName.trim();
        // Check if the department already exists
        const departmentExists = departments.some((department) => {
            const departmentNameLower = department.name.toLowerCase().trim();
            return departmentNameLower === trimmedDepartmentName.toLowerCase();
        });
        if (departmentExists) {
            setError('Department already exists. Please add a different department.');
            setTimeout(() => setError(''), 3000);
            return; // Exit if the department already exists
        }
        setError(''); // Clear any previous error
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return; // Exit if token is not available
            }
            // Send the department data with all fields
            const response = await fetch(`${API_URL}/departments`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedDepartmentName,
                    department_id: departmentId,
                    point_of_contact: pointOfContact,
                    manager,
                    team_lead: teamLead,
                    work_day: workDay,
                    launch_date: launchDate,
                    created_by: createdBy,
                }),
            });
            if (!response.ok) {
                throw new Error('Failed to save department: ' + response.statusText);
            }
            const result = await response.json();
            setSuccessMessage(result?.message || 'Department added successfully.');
            setDepartmentName('');
            setPointOfContact('');
            setManager('');
            setTeamLead('');
            setWorkDay('');
            setLaunchDate('');
    
            // Refetch the departments list after adding the new department
            const newDepartmentsResponse = await fetch(`${API_URL}/departments`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!newDepartmentsResponse.ok) {
                throw new Error('Failed to fetch departments: ' + newDepartmentsResponse.statusText);
            }
    
            const newDepartmentsData = await newDepartmentsResponse.json();
            setDepartments(newDepartmentsData.departments); // Update the departments list
    
        } catch (error) {
            setError('Failed to add department.');
        }
    };
    

    // Only show modal if visible
    if (!isVisible) return null;

    return (
        <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white rounded-lg shadow-md w-full max-w-lg relative">
                <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                    <h3 className="text-base text-left font-medium text-gray-800">Add Department</h3>
                    <button
                        className="text-2xl text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                <form onSubmit={handleSubmit} className='p-6'>
                    <div className="mb-4">
                        <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-2">
                            Department
                        </label>
                        <select
                            id="department"
                            value={departmentId}
                            onChange={(e) => setDepartmentId(e.target.value)}
                            required
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                        >
                            <option value="">Select Department</option>
                            {departments.map((department) => (
                                <option key={department.id} value={department.id}>{department.name}</option>
                            ))}
                        </select>
                    </div>
                    <div className="mb-4">
                        <label htmlFor="departmentName" className="block text-sm font-medium text-gray-700 pb-2">
                            Department Name
                        </label>
                        <input
                            type="text"
                            id="departmentName"
                            value={departmentName}
                            onChange={(e) => setDepartmentName(e.target.value)}
                            required
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="pointOfContact" className="block text-sm font-medium text-gray-700 pb-2">
                            Point of Contact
                        </label>
                        <select
                            id="pointOfContact"
                            value={pointOfContact}
                            onChange={(e) => setPointOfContact(e.target.value)}
                            required
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                        >
                            <option value="">Select Point of Contact</option>
                            {users.map((user) => (
                                <option key={user.id} value={user.id}>{user.fname} {user.lname}</option>
                            ))}
                        </select>
                    </div>
                    <div className="mb-4">
                        <label htmlFor="manager" className="block text-sm font-medium text-gray-700 pb-2">
                            Manager
                        </label>
                        <select
                            id="manager"
                            value={manager}
                            onChange={(e) => setManager(e.target.value)}
                            required
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                        >
                            <option value="">Select Manager</option>
                            {users.map((user) => (
                                <option key={user.id} value={user.id}>{user.fname} {user.lname}</option>
                            ))}
                        </select>
                    </div>
                    <div className="mb-4">
                        <label htmlFor="teamLead" className="block text-sm font-medium text-gray-700 pb-2">
                            Team Lead
                        </label>
                        <select
                            id="teamLead"
                            value={teamLead}
                            onChange={(e) => setTeamLead(e.target.value)}
                            required
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                        >
                            <option value="">Select Team Lead</option>
                            {users.map((user) => (
                                <option key={user.id} value={user.id}>{user.fname} {user.lname}</option>
                            ))}
                        </select>
                    </div>
                    <div className="mb-4">
                        <label htmlFor="workDay" className="block text-sm font-medium text-gray-700 pb-2">
                            Work Day
                        </label>
                        <input
                            type="text"
                            id="workDay"
                            value={workDay}
                            onChange={(e) => setWorkDay(e.target.value)}
                            required
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="launchDate" className="block text-sm font-medium text-gray-700 pb-2">
                            Launch Date
                        </label>
                        <input
                            type="date"
                            id="launchDate"
                            value={launchDate}
                            onChange={(e) => setLaunchDate(e.target.value)}
                            required
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                        />
                    </div>
                    {error && <p className="text-red-500 text-sm">{error}</p>}
                    {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    <div className='py-4'>
                        <button
                            type="submit"
                            className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                        >
                            Add Department
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default AddDepartment;
