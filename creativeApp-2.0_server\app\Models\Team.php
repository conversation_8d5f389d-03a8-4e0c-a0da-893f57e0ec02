<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'icon',
        'logo',
        'poc',
        'manager',
        'team_lead',
        'workday',
        'launch',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'workday' => 'array',
    ];

    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }


    public function users()
    {
        return $this->belongsToMany(User::class);
    }

    // Team can have many schedules
    public function schedules()
    {
        return $this->belongsToMany(Schedule::class);
    }

    // Relation with department
    public function departments()
    {
        return $this->belongsToMany(Department::class);
    }

    // Relation with department
    public function teams()
    {
        return $this->belongsToMany(Team::class);
    }

    public function taskDetails()
    {
        return $this->belongsToMany(Team::class);
    }

    public function time_cards()
    {
        return $this->hasMany(TimeCard::class, 'team_id');
    }

    public function reporters()
    {
        return $this->hasMany(Reporter::class, 'team');
    }
}
