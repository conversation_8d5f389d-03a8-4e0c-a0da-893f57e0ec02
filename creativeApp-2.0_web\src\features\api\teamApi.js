import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const teamApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getTeamData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `team-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['TeamData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    // FIXED: Add endpoint to get teams with department relationships for dropdowns
    getTeamsWithDepartments: builder.query({
      query: () => 'list/teams',
      providesTags: ['TeamData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForTeam: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `team-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['TeamData'],
    }),

    getTeamById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `teams/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'TeamData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createTeam: builder.mutation({
      query: (newFormationType) => ({
        url: 'team-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['TeamData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateTeam: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `teams/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'TeamData', id }, 'TeamData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteTeam: builder.mutation({
      query: (id) => ({
        url: `teams/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['TeamData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetTeamDataQuery,
  useGetTeamsWithDepartmentsQuery,
  useLazyFetchDataOptionsForTeamQuery,
  useGetTeamByIdQuery,
  useLazyGetTeamByIdQuery,
  useCreateTeamMutation,
  useUpdateTeamMutation,
  useDeleteTeamMutation,
} = teamApi;
