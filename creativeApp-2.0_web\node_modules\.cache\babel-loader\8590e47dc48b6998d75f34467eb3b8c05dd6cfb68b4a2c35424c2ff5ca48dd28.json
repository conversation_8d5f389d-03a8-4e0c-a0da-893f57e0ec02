{"ast": null, "code": "import React,{useState,useCallback,useEffect}from\"react\";// DataTable component for rendering tabular data with features like pagination and sorting\nimport DataTable from\"react-data-table-component\";// Loading spinner component to show while data is loading\nimport Loading from\"./../../common/Loading\";import{confirmation<PERSON><PERSON>t,ManageColumns,SearchFilter,TableView}from'./../../common/coreui';import{useDispatch}from\"react-redux\";import{defaultDateTimeFormat,defaultTimeFormat,removeKeys,sortByLabel}from\"./../../utils\";// Libraries for exporting data to Excel\nimport{saveAs}from\"file-saver\";import*as XLSX from\"xlsx\";import{departmentApi,useDeleteDepartmentMutation,useGetDepartmentDataQuery,useLazyFetchDataOptionsForDepartmentQuery}from\"./../../features/api\";import{useNavigate}from\"react-router-dom\";import EditDepartment from\"./EditDepartment\";import AddDepartment from\"./AddDepartment\";import{useRoleBasedAccess}from\"./../../common/useRoleBasedAccess\";import{DateTimeFormatDay,DateTimeFormatHour}from\"../../common/DateTimeFormatTable\";// API endpoint and configuration constants\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const MODULE_NAME=\"Department\";// Main component for listing Product Type List\nconst DepartmentDataList=()=>{// State variables for data items, filters, search text, modals, and loading status\nconst[filterOptions,setFilterOptions]=useState({});const[selectedFilterOptions,setSelectedFilterOptions]=useState({});const[showFilterOption,setShowFilterOption]=useState(\"\");const[queryString,setQueryString]=useState(\"\");const[modalVisible,setModalVisible]=useState(false);const[filterOptionLoading,setFilterOptionLoading]=useState(false);const[dataItemsId,setDataItemsId]=useState(null);const[error,setError]=useState(null);const[viewData,setViewData]=useState(null);const navigate=useNavigate();const[addModalVisible,setAddModalVisible]=useState(false);// Sorting and pagination state\nconst[sortColumn,setSortColumn]=useState(\"created_at\");const[sortDirection,setSortDirection]=useState(\"desc\");const[perPage,setPerPage]=useState(\"10\");const[currentPage,setCurrentPage]=useState(1);const{data:dataItems,isFetching,error:fetchError}=useGetDepartmentDataQuery({sort_by:sortColumn,order:sortDirection,page:currentPage,per_page:perPage,query:queryString});const[triggerFilterByFetch,{data:groupData,error:groupDataError}]=useLazyFetchDataOptionsForDepartmentQuery();const[deleteDepartment]=useDeleteDepartmentMutation();// Build query parameters from selected filters\nconst buildQueryParams=selectedFilters=>{let q=Object.entries(selectedFilters).reduce((acc,_ref)=>{let[key,value]=_ref;if(typeof value===\"string\"){return acc+`&${key}=${value}`;}if(Array.isArray(value)){const vals=value.map(i=>i.value).join(\",\");return acc+`&${key}=${vals}`;}return acc;},\"\");setQueryString(q);};const handleCopy=data=>{const keysToRemove=[\"id\",\"team\",\"department\",\"updated_at\",\"updated_by\",\"updater\",\"created_at\",\"creator\",\"created_by\",\"updated_by\"];const cleanedData=removeKeys(data,keysToRemove);setViewData(null);setModalVisible(true);};const handleEdit=id=>{setViewData(null);setDataItemsId(id);setModalVisible(true);};const handleDelete=id=>{confirmationAlert({onConfirm:()=>{deleteDepartment(id);setViewData(null);}});};let columnSerial=1;const{rolePermissions}=useRoleBasedAccess();// Define columns dynamically based on rolePermissions\nconst[columns,setColumns]=useState(()=>[{id:columnSerial++,name:\"Action\",width:\"180px\",className:\"bg-red-300\",cell:item=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-1 mx-2 !min-w-[200px] pl-3\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",onClick:()=>setViewData(item),children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-lg\",children:\"visibility\"})}),(rolePermissions===null||rolePermissions===void 0?void 0:rolePermissions.hasManagerRole)&&/*#__PURE__*/_jsx(\"button\",{className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",onClick:()=>handleEdit(item.id),children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-lg\",children:\"stylus_note\"})}),(rolePermissions===null||rolePermissions===void 0?void 0:rolePermissions.hasManagerRole)&&/*#__PURE__*/_jsx(\"button\",{className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",onClick:()=>handleCopy(item),children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-lg\",children:\"content_copy\"})}),(rolePermissions===null||rolePermissions===void 0?void 0:rolePermissions.hasManagerRole)&&/*#__PURE__*/_jsx(\"button\",{className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",onClick:()=>handleDelete(item.id),children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"delete\"})})]})},{id:columnSerial++,name:\"S.No\",selector:(row,index)=>(currentPage-1)*perPage+index+1,width:\"80px\",omit:false},{id:columnSerial++,name:\"Department Name\",db_field:\"name\",selector:row=>row.name||\"\",omit:false,sortable:true,filterable:true},{id:columnSerial++,name:\"HOD\",db_field:\"hod\",selector:row=>row.hod||\"\",omit:false,sortable:true,filterable:true},{id:columnSerial++,name:\"Launch Date\",db_field:\"launch_date\",selector:row=>row.launch_date||\"\",omit:false,sortable:true,filterable:true},{id:columnSerial++,name:\"Created by\",selector:row=>{var _row$creator,_row$creator2;return`${((_row$creator=row.creator)===null||_row$creator===void 0?void 0:_row$creator.fname)||\"\"} ${((_row$creator2=row.creator)===null||_row$creator2===void 0?void 0:_row$creator2.lname)||\"\"}`;},db_field:\"created_by\",omit:false,sortable:true,filterable:true},{id:columnSerial++,name:\"Created Date\",selector:row=>DateTimeFormatDay(row.created_at),db_field:\"created_at\",omit:false,sortable:true,filterable:true},{id:columnSerial++,name:\"Created Time\",selector:row=>DateTimeFormatHour(row.created_at),db_field:\"created_at\",omit:false,sortable:true,filterable:false},{id:columnSerial++,name:\"Updated by\",selector:row=>{var _row$updater,_row$updater2;return`${((_row$updater=row.updater)===null||_row$updater===void 0?void 0:_row$updater.fname)||\"\"} ${((_row$updater2=row.updater)===null||_row$updater2===void 0?void 0:_row$updater2.lname)||\"\"}`;},db_field:\"updated_by\",omit:false,sortable:true,filterable:true},{id:columnSerial++,name:\"Updated Date\",selector:row=>DateTimeFormatDay(row.updated_at),db_field:\"updated_at\",omit:false,sortable:true,filterable:true},{id:columnSerial++,name:\"Updated Time\",selector:row=>DateTimeFormatHour(row.updated_at),db_field:\"updated_at\",omit:false,sortable:true,filterable:false}]);useEffect(()=>{// Recalculate or update columns if rolePermissions change\nsetColumns(prevColumns=>[...prevColumns.map(col=>{if(col.name===\"Action\"){// Update the \"Action\" column dynamically\nreturn{...col,cell:item=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-1 mx-2 !min-w-[200px] pl-3\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",onClick:()=>setViewData(item),children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-lg\",children:\"visibility\"})}),(rolePermissions===null||rolePermissions===void 0?void 0:rolePermissions.hasManagerRole)&&/*#__PURE__*/_jsx(\"button\",{className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",onClick:()=>handleEdit(item.id),children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-lg\",children:\"stylus_note\"})}),(rolePermissions===null||rolePermissions===void 0?void 0:rolePermissions.hasManagerRole)&&/*#__PURE__*/_jsx(\"button\",{className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",onClick:()=>handleCopy(item),children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-lg\",children:\"content_copy\"})}),(rolePermissions===null||rolePermissions===void 0?void 0:rolePermissions.hasManagerRole)&&/*#__PURE__*/_jsx(\"button\",{className:\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",onClick:()=>handleDelete(item.id),children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"delete\"})})]})};}return col;})]);},[rolePermissions]);// Dependency array ensures this is updated whenever rolePermissions changes\n// Resets the pagination and clear-all filter state\nconst resetPage=()=>{if(Object.keys(selectedFilterOptions).length){let newObj={};Object.keys(selectedFilterOptions).map(key=>{if(typeof selectedFilterOptions[key]===\"string\"){newObj[key]=\"\";}else{newObj[key]=[];}});setSelectedFilterOptions({...newObj});buildQueryParams({...newObj});}setCurrentPage(1);};// Export the fetched data into an Excel file\nconst dispatch=useDispatch();const exportToExcel=async()=>{try{// Fetch all data items for Excel export\nconst result=await dispatch(departmentApi.endpoints.getDepartmentData.initiate({sort_by:sortColumn,order:sortDirection,page:currentPage,per_page:(dataItems===null||dataItems===void 0?void 0:dataItems.total)||10,// Fallback value to avoid undefined issues\nquery:queryString})).unwrap();// Wait for the API response\nif(!(result!==null&&result!==void 0&&result.total)||result.total<1){return false;}var sl=1;let prepXlsData=result.data.map(item=>{if(columns.length){let obj={};columns.forEach(column=>{if(!column.omit&&column.selector){obj[column.name]=column.name===\"S.No\"?sl++:column.selector(item)||\"\";}});return obj;}});// Create a worksheet from the JSON data and append to a new workbook\nconst worksheet=XLSX.utils.json_to_sheet(prepXlsData);const workbook=XLSX.utils.book_new();XLSX.utils.book_append_sheet(workbook,worksheet,\"Sheet1\");// Convert workbook to a buffer and create a Blob to trigger a file download\nconst excelBuffer=XLSX.write(workbook,{bookType:\"xlsx\",type:\"array\"});const blob=new Blob([excelBuffer],{type:\"application/octet-stream\"});saveAs(blob,`${MODULE_NAME.replace(/ /g,\"_\")}_${prepXlsData.length}.xlsx`);}catch(error){console.error(\"Error exporting to Excel:\",error);}};/**\r\n   * Fetch filter options from API for a specific field.\r\n   */const fetchDataOptionsForFilterBy=useCallback(async function(){let itemObject=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"group\";let searching=arguments.length>2&&arguments[2]!==undefined?arguments[2]:\"\";let fieldType=arguments.length>3&&arguments[3]!==undefined?arguments[3]:\"select\";let groupByField=itemObject.db_field||\"title\";try{setShowFilterOption(groupByField);setFilterOptionLoading(true);var groupData=[];const response=await triggerFilterByFetch({type:type.trim(),column:groupByField.trim(),text:searching.trim()});if(response.data){groupData=response.data;}if(groupData.length){if(fieldType===\"searchable\"){setFilterOptions(prev=>({...prev,[groupByField]:groupData}));return groupData;}const optionsForFilter=groupData.map(item=>{if(itemObject.selector){let label=itemObject.selector(item);if(label){if(item.total&&item.total>1){label+=` (${item.total})`;}return{label,value:item[groupByField]};}return null;}}).filter(Boolean);setFilterOptions(prev=>({...prev,[itemObject.id]:sortByLabel(optionsForFilter)}));return optionsForFilter;}}catch(error){setError(error.message);}finally{setFilterOptionLoading(false);}},[]);return/*#__PURE__*/_jsx(\"section\",{className:\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mx-auto pb-6 \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-4/12 md:w-10/12 text-start\",children:/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold \",children:MODULE_NAME})}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-8/12 flex items-end justify-end gap-1\",children:[/*#__PURE__*/_jsx(ManageColumns,{columns:columns,setColumns:setColumns}),!isFetching&&dataItems&&parseInt(dataItems.total)>0&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(\"button\",{className:\"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\",onClick:exportToExcel,children:[isFetching&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined animate-spin text-sm me-2\",children:\"progress_activity\"})}),!isFetching&&/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm me-2\",children:\"file_export\"}),\"Export to Excel (\",dataItems.total,\")\"]})}),rolePermissions.hasManagerRole&&/*#__PURE__*/_jsx(\"button\",{className:\" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\",onClick:()=>setAddModalVisible(true),children:\"Add New\"})]})]}),/*#__PURE__*/_jsx(SearchFilter,{columns:columns,selectedFilterOptions:selectedFilterOptions,setSelectedFilterOptions:setSelectedFilterOptions,fetchDataOptionsForFilterBy:fetchDataOptionsForFilterBy,filterOptions:filterOptions,filterOptionLoading:filterOptionLoading,showFilterOption:showFilterOption,resetPage:resetPage,setCurrentPage:setCurrentPage,buildQueryParams:buildQueryParams}),fetchError&&/*#__PURE__*/_jsx(\"div\",{className:\"text-red-500\",children:error}),isFetching&&/*#__PURE__*/_jsx(Loading,{}),/*#__PURE__*/_jsx(\"div\",{className:\"border border-gray-200 p-0 pb-1 rounded-lg my-5 \",children:/*#__PURE__*/_jsx(DataTable,{columns:columns,data:(dataItems===null||dataItems===void 0?void 0:dataItems.data)||[],className:\"p-0 scrollbar-horizontal-10\",fixedHeader:true,highlightOnHover:true,responsive:true,pagination:true,paginationServer:true,paginationPerPage:perPage,paginationTotalRows:(dataItems===null||dataItems===void 0?void 0:dataItems.total)||0,onChangePage:page=>{if(page!==currentPage){setCurrentPage(page);}},onChangeRowsPerPage:newPerPage=>{if(newPerPage!==perPage){setPerPage(newPerPage);setCurrentPage(1);}},paginationComponentOptions:{selectAllRowsItem:true,selectAllRowsItemText:\"ALL\"},sortServer:true,onSort:function(column){let sortDirection=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"desc\";if(Object.keys(column).length){setSortColumn(column.db_field||column.name||\"created_at\");setSortDirection(sortDirection||\"desc\");}}})}),addModalVisible&&/*#__PURE__*/_jsx(AddDepartment,{isVisible:addModalVisible,setVisible:setAddModalVisible}),modalVisible&&/*#__PURE__*/_jsx(EditDepartment,{isVisible:modalVisible,setVisible:setModalVisible,dataItemsId:dataItemsId}),viewData&&/*#__PURE__*/// <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\n_jsx(TableView,{item:viewData,setViewData:setViewData,columns:columns,handleEdit:handleEdit,handleDelete:handleDelete})]})});};export default DepartmentDataList;", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "DataTable", "Loading", "<PERSON><PERSON><PERSON><PERSON>", "ManageColumns", "SearchFilter", "TableView", "useDispatch", "defaultDateTimeFormat", "defaultTimeFormat", "<PERSON><PERSON><PERSON><PERSON>", "sortByLabel", "saveAs", "XLSX", "departmentApi", "useDeleteDepartmentMutation", "useGetDepartmentDataQuery", "useLazyFetchDataOptionsForDepartmentQuery", "useNavigate", "EditDepartment", "AddDepartment", "useRoleBasedAccess", "DateTimeFormatDay", "DateTimeFormatHour", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "MODULE_NAME", "DepartmentDataList", "filterOptions", "setFilterOptions", "selectedFilterOptions", "setSelectedFilterOptions", "showFilterOption", "setShowFilterOption", "queryString", "setQueryString", "modalVisible", "setModalVisible", "filterOptionLoading", "setFilterOptionLoading", "dataItemsId", "setDataItemsId", "error", "setError", "viewData", "setViewData", "navigate", "addModalVisible", "setAddModalVisible", "sortColumn", "setSortColumn", "sortDirection", "setSortDirection", "perPage", "setPerPage", "currentPage", "setCurrentPage", "data", "dataItems", "isFetching", "fetchError", "sort_by", "order", "page", "per_page", "query", "triggerFilterByFetch", "groupData", "groupDataError", "deleteDepartment", "buildQueryParams", "selectedFilters", "q", "Object", "entries", "reduce", "acc", "_ref", "key", "value", "Array", "isArray", "vals", "map", "i", "join", "handleCopy", "keysToRemove", "cleanedData", "handleEdit", "id", "handleDelete", "onConfirm", "columnSerial", "rolePermissions", "columns", "setColumns", "name", "width", "className", "cell", "item", "children", "onClick", "hasManagerRole", "selector", "row", "index", "omit", "db_field", "sortable", "filterable", "hod", "launch_date", "_row$creator", "_row$creator2", "creator", "fname", "lname", "created_at", "_row$updater", "_row$updater2", "updater", "updated_at", "prevColumns", "col", "resetPage", "keys", "length", "newObj", "dispatch", "exportToExcel", "result", "endpoints", "getDepartmentData", "initiate", "total", "unwrap", "sl", "prepXlsData", "obj", "for<PERSON>ach", "column", "worksheet", "utils", "json_to_sheet", "workbook", "book_new", "book_append_sheet", "excelBuffer", "write", "bookType", "type", "blob", "Blob", "replace", "console", "fetchDataOptionsForFilterBy", "itemObject", "arguments", "undefined", "searching", "fieldType", "groupByField", "response", "trim", "text", "prev", "optionsForFilter", "label", "filter", "Boolean", "message", "parseInt", "fixedHeader", "highlightOnHover", "responsive", "pagination", "paginationServer", "paginationPerPage", "paginationTotalRows", "onChangePage", "onChangeRowsPerPage", "newPerPage", "paginationComponentOptions", "selectAllRowsItem", "selectAllRowsItemText", "sortServer", "onSort", "isVisible", "setVisible"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/pages/department/DepartmentDataList.jsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect } from \"react\";\r\n\r\n// DataTable component for rendering tabular data with features like pagination and sorting\r\nimport DataTable from \"react-data-table-component\";\r\n\r\n// Loading spinner component to show while data is loading\r\nimport Loading from \"./../../common/Loading\";\r\n\r\nimport {confirmation<PERSON><PERSON>t, ManageColumns, SearchFilter, TableView} from './../../common/coreui';\r\n\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { defaultDateTimeFormat, defaultTimeFormat, removeKeys, sortByLabel } from \"./../../utils\";\r\n\r\n// Libraries for exporting data to Excel\r\nimport { saveAs } from \"file-saver\";\r\nimport * as XLSX from \"xlsx\";\r\nimport { departmentApi, useDeleteDepartmentMutation, useGetDepartmentDataQuery, useLazyFetchDataOptionsForDepartmentQuery } from \"./../../features/api\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport EditDepartment from \"./EditDepartment\";\r\nimport AddDepartment from \"./AddDepartment\";\r\nimport { useRoleBasedAccess } from \"./../../common/useRoleBasedAccess\";\r\nimport { DateTimeFormatDay, DateTimeFormatHour } from \"../../common/DateTimeFormatTable\";\r\n\r\n// API endpoint and configuration constants\r\nconst MODULE_NAME = \"Department\";\r\n\r\n// Main component for listing Product Type List\r\nconst DepartmentDataList = () => {\r\n  // State variables for data items, filters, search text, modals, and loading status\r\n  const [filterOptions, setFilterOptions] = useState({});\r\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\r\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\r\n  const [queryString, setQueryString] = useState(\"\");\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\r\n  const [dataItemsId, setDataItemsId] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  const [viewData, setViewData] = useState(null);\r\n  const navigate = useNavigate();\r\n  const [addModalVisible, setAddModalVisible] = useState(false);\r\n\r\n  \r\n  // Sorting and pagination state\r\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\r\n  const [sortDirection, setSortDirection] = useState(\"desc\");\r\n  const [perPage, setPerPage] = useState(\"10\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  \r\n  const { data: dataItems, isFetching, error: fetchError } = useGetDepartmentDataQuery({ sort_by: sortColumn, order: sortDirection, page: currentPage, per_page: perPage, query: queryString });\r\n\r\n  const [triggerFilterByFetch, { data: groupData, error: groupDataError }] = useLazyFetchDataOptionsForDepartmentQuery();\r\n       \r\n  const [deleteDepartment] = useDeleteDepartmentMutation();\r\n\r\n  // Build query parameters from selected filters\r\n  const buildQueryParams = (selectedFilters) => {\r\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\r\n      if (typeof value === \"string\") {\r\n        return acc + `&${key}=${value}`;\r\n      }\r\n      if (Array.isArray(value)) {\r\n        const vals = value.map((i) => i.value).join(\",\");\r\n        return acc + `&${key}=${vals}`;\r\n      }\r\n      return acc;\r\n    }, \"\")\r\n\r\n    setQueryString(q);\r\n  }\r\n\r\n  const handleCopy = (data) => {\r\n    const keysToRemove = [\"id\", \"team\", \"department\", \"updated_at\", \"updated_by\", \"updater\", \"created_at\", \"creator\", \"created_by\", \"updated_by\"];\r\n    const cleanedData = removeKeys(data, keysToRemove);\r\n    setViewData(null)\r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleEdit = (id) => {\r\n    setViewData(null)\r\n    setDataItemsId(id); \r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleDelete = (id) => {\r\n    confirmationAlert({onConfirm: () => \r\n      {        \r\n        deleteDepartment(id);\r\n        setViewData(null);\r\n      }});  \r\n  }\r\n \r\n\r\n  let columnSerial = 1;\r\n\r\n  const { rolePermissions } = useRoleBasedAccess();\r\n\r\n  // Define columns dynamically based on rolePermissions\r\n  const [columns, setColumns] = useState(() => [\r\n    {\r\n        id: columnSerial++,\r\n      name: \"Action\",\r\n      width: \"180px\",\r\n      className: \"bg-red-300\",\r\n      cell: (item) => (\r\n        <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n          {/* View Button */}\r\n          <button\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            onClick={() => setViewData(item)}\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n          </button>\r\n  \r\n          {/* Conditionally render Edit Button based on rolePermissions */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleEdit(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Copy Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleCopy(item)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Delete Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleDelete(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n      name: \"S.No\",\r\n      selector: (row, index) => (currentPage - 1) * perPage + index + 1,\r\n      width: \"80px\",\r\n      omit: false,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Department Name\",\r\n      db_field: \"name\",\r\n      selector: (row) => row.name || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"HOD\",\r\n      db_field: \"hod\",\r\n      selector: (row) => row.hod || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Launch Date\",\r\n      db_field: \"launch_date\",\r\n      selector: (row) => row.launch_date || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    \r\n    {\r\n        id: columnSerial++,\r\n        name: \"Created by\",\r\n        selector: (row) => `${row.creator?.fname || \"\"} ${row.creator?.lname || \"\"}`,\r\n        db_field: \"created_by\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Created Date\",\r\n      selector: (row) => DateTimeFormatDay(row.created_at),\r\n      db_field: \"created_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Created Time\",\r\n        selector: (row) => DateTimeFormatHour(row.created_at),\r\n        db_field: \"created_at\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: false,\r\n      },\r\n      {\r\n        id: columnSerial++,\r\n        name: \"Updated by\",\r\n        selector: (row) => `${row.updater?.fname || \"\"} ${row.updater?.lname || \"\"}`,\r\n        db_field: \"updated_by\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: true,\r\n      },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Updated Date\",\r\n      selector: (row) => DateTimeFormatDay(row.updated_at),\r\n      db_field: \"updated_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n        id: columnSerial++,\r\n        name: \"Updated Time\",\r\n        selector: (row) => DateTimeFormatHour(row.updated_at),\r\n        db_field: \"updated_at\",\r\n        omit: false,\r\n        sortable: true,\r\n        filterable: false,\r\n    },\r\n  ]);\r\n  \r\n  useEffect(() => {\r\n    // Recalculate or update columns if rolePermissions change\r\n    setColumns((prevColumns) => [\r\n      ...prevColumns.map((col) => {\r\n        if (col.name === \"Action\") {\r\n          // Update the \"Action\" column dynamically\r\n          return {\r\n            ...col,\r\n            cell: (item) => (\r\n              <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => setViewData(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n                </button>\r\n                {rolePermissions?.hasManagerRole && (\r\n                  <button\r\n                    className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                    onClick={() => handleEdit(item.id)}\r\n                  >\r\n                    <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n                  </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleCopy(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n                </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleDelete(item.id)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n                </button>\r\n                )}\r\n              </div>\r\n            ),\r\n          };\r\n        }\r\n        return col;\r\n      }),\r\n    ]);\r\n  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes\r\n  \r\n  \r\n\r\n  // Resets the pagination and clear-all filter state\r\n  const resetPage = () => {\r\n    if (Object.keys(selectedFilterOptions).length) {\r\n      let newObj = {};\r\n      Object.keys(selectedFilterOptions).map((key) => {\r\n        if (typeof selectedFilterOptions[key] === \"string\") {\r\n          newObj[key] = \"\";\r\n        } else {\r\n          newObj[key] = [];\r\n        }\r\n      });\r\n      setSelectedFilterOptions({ ...newObj });\r\n      buildQueryParams({ ...newObj })\r\n    }\r\n    setCurrentPage(1);\r\n  };\r\n\r\n\r\n  // Export the fetched data into an Excel file\r\n  const dispatch = useDispatch();\r\n  const exportToExcel = async () => {\r\n    try {\r\n      // Fetch all data items for Excel export\r\n      const result = await dispatch(\r\n        departmentApi.endpoints.getDepartmentData.initiate({\r\n          sort_by: sortColumn,\r\n          order: sortDirection,\r\n          page: currentPage,\r\n          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues\r\n          query: queryString,\r\n        })\r\n      ).unwrap(); // Wait for the API response\r\n  \r\n      if (!result?.total || result.total < 1) {\r\n        return false;\r\n      }\r\n  \r\n      var sl = 1;\r\n  \r\n      let prepXlsData = result.data.map((item) => {\r\n        if (columns.length) {\r\n          let obj = {};\r\n          columns.forEach((column) => {\r\n            if (!column.omit && column.selector) {\r\n              obj[column.name] = column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\r\n            }\r\n          });\r\n          return obj;\r\n        }\r\n      });\r\n  \r\n      // Create a worksheet from the JSON data and append to a new workbook\r\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\r\n      const workbook = XLSX.utils.book_new();\r\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\r\n  \r\n      // Convert workbook to a buffer and create a Blob to trigger a file download\r\n      const excelBuffer = XLSX.write(workbook, {\r\n        bookType: \"xlsx\",\r\n        type: \"array\",\r\n      });\r\n      const blob = new Blob([excelBuffer], { type: \"application/octet-stream\" });\r\n      saveAs(blob, `${MODULE_NAME.replace(/ /g,\"_\")}_${prepXlsData.length}.xlsx`);\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n    }\r\n  };\r\n  \r\n\r\n  /**\r\n   * Fetch filter options from API for a specific field.\r\n   */\r\n  const fetchDataOptionsForFilterBy = useCallback(\r\n    async (\r\n      itemObject = {},\r\n      type = \"group\",\r\n      searching = \"\",\r\n      fieldType = \"select\"\r\n    ) => {\r\n\r\n      let groupByField = itemObject.db_field || \"title\";\r\n\r\n      try {\r\n        setShowFilterOption(groupByField);\r\n        setFilterOptionLoading(true);\r\n\r\n        var groupData = [];\r\n\r\n        const response = await triggerFilterByFetch({ type: type.trim(), column: groupByField.trim(), text: searching.trim() });\r\n        \r\n        if (response.data) {\r\n          groupData = response.data;\r\n        }\r\n\r\n        if (groupData.length) {\r\n\r\n          if (fieldType === \"searchable\") {\r\n            setFilterOptions((prev) => ({\r\n              ...prev,\r\n              [groupByField]: groupData,\r\n            }));\r\n\r\n            return groupData;\r\n          }\r\n\r\n          const optionsForFilter = groupData\r\n            .map((item) => {\r\n              if(itemObject.selector){\r\n                let label = itemObject.selector(item);\r\n\r\n                if(label){\r\n                  if (item.total && item.total > 1) {\r\n                    label += ` (${item.total})`;\r\n                  }\r\n\r\n                  return { label, value: item[groupByField] };\r\n                }\r\n\r\n              return null;\r\n              }\r\n            }).filter(Boolean);\r\n\r\n          setFilterOptions((prev) => ({\r\n            ...prev,\r\n            [itemObject.id]: sortByLabel(optionsForFilter),\r\n          }));\r\n\r\n          return optionsForFilter;\r\n        }\r\n      } catch (error) {\r\n        setError(error.message);\r\n      } finally {\r\n        setFilterOptionLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  return (\r\n    <section className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      <div className=\"mx-auto pb-6 \">\r\n        {/* Header section with title and action buttons */}\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4\">\r\n          <div className=\"w-4/12 md:w-10/12 text-start\">\r\n            <h2 className=\"text-2xl font-bold \">{MODULE_NAME}</h2>\r\n          </div>\r\n          <div className=\"w-8/12 flex items-end justify-end gap-1\">\r\n            {/* Manage Columns dropdown */}\r\n            <ManageColumns columns={columns} setColumns={setColumns} />\r\n            \r\n            {/* Export to Excel button, only shown if data exists */}\r\n            { !isFetching && dataItems && parseInt(dataItems.total) > 0 && (\r\n              <>\r\n                <button\r\n                  className=\"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n                  onClick={exportToExcel}\r\n                >\r\n                  {isFetching && (\r\n                    <>\r\n                      <span className=\"material-symbols-outlined animate-spin text-sm me-2\">\r\n                        progress_activity\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                  {!isFetching && (\r\n                    <span className=\"material-symbols-outlined text-sm me-2\">\r\n                    file_export\r\n                    </span>\r\n                  )}\r\n                  Export to Excel ({dataItems.total})\r\n                </button>\r\n              </>\r\n            )}\r\n            {/* Button to open modal for adding a new formation */}\r\n            {rolePermissions.hasManagerRole && (\r\n              <button\r\n                className=\" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n\r\n                onClick={() => setAddModalVisible(true)}\r\n              >\r\n                Add New\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filter fieldset for global search and field-specific filtering */}\r\n        <SearchFilter\r\n            columns={columns}\r\n            selectedFilterOptions={selectedFilterOptions}\r\n            setSelectedFilterOptions={setSelectedFilterOptions}\r\n            fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}\r\n            filterOptions={filterOptions}\r\n            filterOptionLoading={filterOptionLoading}\r\n            showFilterOption={showFilterOption}\r\n            resetPage={resetPage}\r\n            setCurrentPage={setCurrentPage}\r\n            buildQueryParams={buildQueryParams}\r\n        />\r\n\r\n        {/* Display error message if any error occurs */}\r\n        {fetchError && <div className=\"text-red-500\">{error}</div>}\r\n        {/* Show loading spinner when data is being fetched */}\r\n        {isFetching && <Loading />}\r\n\r\n        {/* If no data is available, display an alert message */}\r\n        \r\n        {/* Render the DataTable with the fetched data */}\r\n        <div className=\"border border-gray-200 p-0 pb-1 rounded-lg my-5 \">\r\n          <DataTable\r\n            columns={columns}\r\n            data={dataItems?.data || []}\r\n            className=\"p-0 scrollbar-horizontal-10\"\r\n            fixedHeader\r\n            \r\n            highlightOnHover\r\n            responsive\r\n            pagination\r\n            paginationServer\r\n            paginationPerPage={perPage}\r\n            paginationTotalRows={dataItems?.total || 0}\r\n            onChangePage={(page) => {\r\n              if (page !== currentPage) {\r\n                setCurrentPage(page);\r\n              }\r\n            }}\r\n            onChangeRowsPerPage={(newPerPage) => {\r\n              if(newPerPage !== perPage){\r\n                setPerPage(newPerPage);\r\n                setCurrentPage(1);\r\n              }\r\n            }}\r\n            paginationComponentOptions={{\r\n              selectAllRowsItem: true,\r\n              selectAllRowsItemText: \"ALL\",\r\n            }}\r\n            sortServer\r\n            onSort={(column, sortDirection=\"desc\") => {\r\n              if(Object.keys(column).length){\r\n                setSortColumn(column.db_field || column.name || \"created_at\");\r\n                setSortDirection(sortDirection || \"desc\");\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Add Modal */}\r\n        {addModalVisible && (\r\n            <AddDepartment\r\n                isVisible={addModalVisible}\r\n                setVisible={setAddModalVisible}\r\n            />\r\n        )}\r\n\r\n        {/* Conditionally render the Edit modal */}\r\n        {modalVisible && (\r\n          <EditDepartment\r\n            isVisible={modalVisible}\r\n            setVisible={setModalVisible}\r\n            dataItemsId={dataItemsId}\r\n          />\r\n        )}\r\n\r\n        {viewData && (\r\n          // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n          <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n        )}\r\n       \r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\n\r\nexport default DepartmentDataList;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,WAAW,CAAEC,SAAS,KAAQ,OAAO,CAE/D;AACA,MAAO,CAAAC,SAAS,KAAM,4BAA4B,CAElD;AACA,MAAO,CAAAC,OAAO,KAAM,wBAAwB,CAE5C,OAAQC,iBAAiB,CAAEC,aAAa,CAAEC,YAAY,CAAEC,SAAS,KAAO,uBAAuB,CAG/F,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,qBAAqB,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,WAAW,KAAQ,eAAe,CAEjG;AACA,OAASC,MAAM,KAAQ,YAAY,CACnC,MAAO,GAAK,CAAAC,IAAI,KAAM,MAAM,CAC5B,OAASC,aAAa,CAAEC,2BAA2B,CAAEC,yBAAyB,CAAEC,yCAAyC,KAAQ,sBAAsB,CACvJ,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAC3C,OAASC,kBAAkB,KAAQ,mCAAmC,CACtE,OAASC,iBAAiB,CAAEC,kBAAkB,KAAQ,kCAAkC,CAExF;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,WAAW,CAAG,YAAY,CAEhC;AACA,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B;AACA,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC,CACtD,KAAM,CAACoC,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC,CACtE,KAAM,CAACsC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACwC,WAAW,CAAEC,cAAc,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC0C,YAAY,CAAEC,eAAe,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC4C,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG7C,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAAC8C,WAAW,CAAEC,cAAc,CAAC,CAAG/C,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACgD,KAAK,CAAEC,QAAQ,CAAC,CAAGjD,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACkD,QAAQ,CAAEC,WAAW,CAAC,CAAGnD,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAAAoD,QAAQ,CAAGhC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACiC,eAAe,CAAEC,kBAAkB,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAG7D;AACA,KAAM,CAACuD,UAAU,CAAEC,aAAa,CAAC,CAAGxD,QAAQ,CAAC,YAAY,CAAC,CAC1D,KAAM,CAACyD,aAAa,CAAEC,gBAAgB,CAAC,CAAG1D,QAAQ,CAAC,MAAM,CAAC,CAC1D,KAAM,CAAC2D,OAAO,CAAEC,UAAU,CAAC,CAAG5D,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC6D,WAAW,CAAEC,cAAc,CAAC,CAAG9D,QAAQ,CAAC,CAAC,CAAC,CAGjD,KAAM,CAAE+D,IAAI,CAAEC,SAAS,CAAEC,UAAU,CAAEjB,KAAK,CAAEkB,UAAW,CAAC,CAAGhD,yBAAyB,CAAC,CAAEiD,OAAO,CAAEZ,UAAU,CAAEa,KAAK,CAAEX,aAAa,CAAEY,IAAI,CAAER,WAAW,CAAES,QAAQ,CAAEX,OAAO,CAAEY,KAAK,CAAE/B,WAAY,CAAC,CAAC,CAE7L,KAAM,CAACgC,oBAAoB,CAAE,CAAET,IAAI,CAAEU,SAAS,CAAEzB,KAAK,CAAE0B,cAAe,CAAC,CAAC,CAAGvD,yCAAyC,CAAC,CAAC,CAEtH,KAAM,CAACwD,gBAAgB,CAAC,CAAG1D,2BAA2B,CAAC,CAAC,CAExD;AACA,KAAM,CAAA2D,gBAAgB,CAAIC,eAAe,EAAK,CAC5C,GAAI,CAAAC,CAAC,CAAGC,MAAM,CAACC,OAAO,CAACH,eAAe,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,CAAAC,IAAA,GAAmB,IAAjB,CAACC,GAAG,CAAEC,KAAK,CAAC,CAAAF,IAAA,CAC/D,GAAI,MAAO,CAAAE,KAAK,GAAK,QAAQ,CAAE,CAC7B,MAAO,CAAAH,GAAG,CAAG,IAAIE,GAAG,IAAIC,KAAK,EAAE,CACjC,CACA,GAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,CAAE,CACxB,KAAM,CAAAG,IAAI,CAAGH,KAAK,CAACI,GAAG,CAAEC,CAAC,EAAKA,CAAC,CAACL,KAAK,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC,CAChD,MAAO,CAAAT,GAAG,CAAG,IAAIE,GAAG,IAAII,IAAI,EAAE,CAChC,CACA,MAAO,CAAAN,GAAG,CACZ,CAAC,CAAE,EAAE,CAAC,CAENzC,cAAc,CAACqC,CAAC,CAAC,CACnB,CAAC,CAED,KAAM,CAAAc,UAAU,CAAI7B,IAAI,EAAK,CAC3B,KAAM,CAAA8B,YAAY,CAAG,CAAC,IAAI,CAAE,MAAM,CAAE,YAAY,CAAE,YAAY,CAAE,YAAY,CAAE,SAAS,CAAE,YAAY,CAAE,SAAS,CAAE,YAAY,CAAE,YAAY,CAAC,CAC7I,KAAM,CAAAC,WAAW,CAAGlF,UAAU,CAACmD,IAAI,CAAE8B,YAAY,CAAC,CAClD1C,WAAW,CAAC,IAAI,CAAC,CACjBR,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAAoD,UAAU,CAAIC,EAAE,EAAK,CACzB7C,WAAW,CAAC,IAAI,CAAC,CACjBJ,cAAc,CAACiD,EAAE,CAAC,CAClBrD,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAAsD,YAAY,CAAID,EAAE,EAAK,CAC3B3F,iBAAiB,CAAC,CAAC6F,SAAS,CAAEA,CAAA,GAC5B,CACEvB,gBAAgB,CAACqB,EAAE,CAAC,CACpB7C,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAAC,CAAC,CACP,CAAC,CAGD,GAAI,CAAAgD,YAAY,CAAG,CAAC,CAEpB,KAAM,CAAEC,eAAgB,CAAC,CAAG7E,kBAAkB,CAAC,CAAC,CAEhD;AACA,KAAM,CAAC8E,OAAO,CAAEC,UAAU,CAAC,CAAGtG,QAAQ,CAAC,IAAM,CAC3C,CACIgG,EAAE,CAAEG,YAAY,EAAE,CACpBI,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,YAAY,CACvBC,IAAI,CAAGC,IAAI,eACT9E,KAAA,QAAK4E,SAAS,CAAC,qCAAqC,CAAAG,QAAA,eAElDjF,IAAA,WACE8E,SAAS,CAAC,uLAAuL,CACjMI,OAAO,CAAEA,CAAA,GAAM1D,WAAW,CAACwD,IAAI,CAAE,CAAAC,QAAA,cAEjCjF,IAAA,SAAM8E,SAAS,CAAC,mCAAmC,CAAAG,QAAA,CAAC,YAAU,CAAM,CAAC,CAC/D,CAAC,CAGR,CAAAR,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEU,cAAc,gBAC9BnF,IAAA,WACE8E,SAAS,CAAC,mLAAmL,CAC7LI,OAAO,CAAEA,CAAA,GAAMd,UAAU,CAACY,IAAI,CAACX,EAAE,CAAE,CAAAY,QAAA,cAEnCjF,IAAA,SAAM8E,SAAS,CAAC,mCAAmC,CAAAG,QAAA,CAAC,aAAW,CAAM,CAAC,CAChE,CACT,CAGA,CAAAR,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEU,cAAc,gBAC9BnF,IAAA,WACE8E,SAAS,CAAC,qLAAqL,CAC/LI,OAAO,CAAEA,CAAA,GAAMjB,UAAU,CAACe,IAAI,CAAE,CAAAC,QAAA,cAEhCjF,IAAA,SAAM8E,SAAS,CAAC,mCAAmC,CAAAG,QAAA,CAAC,cAAY,CAAM,CAAC,CACjE,CACT,CAGA,CAAAR,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEU,cAAc,gBAC9BnF,IAAA,WACE8E,SAAS,CAAC,mLAAmL,CAC7LI,OAAO,CAAEA,CAAA,GAAMZ,YAAY,CAACU,IAAI,CAACX,EAAE,CAAE,CAAAY,QAAA,cAErCjF,IAAA,SAAM8E,SAAS,CAAC,mCAAmC,CAAAG,QAAA,CAAC,QAAM,CAAM,CAAC,CAC3D,CACT,EACE,CAET,CAAC,CACD,CACIZ,EAAE,CAAEG,YAAY,EAAE,CACpBI,IAAI,CAAE,MAAM,CACZQ,QAAQ,CAAEA,CAACC,GAAG,CAAEC,KAAK,GAAK,CAACpD,WAAW,CAAG,CAAC,EAAIF,OAAO,CAAGsD,KAAK,CAAG,CAAC,CACjET,KAAK,CAAE,MAAM,CACbU,IAAI,CAAE,KACR,CAAC,CACD,CACElB,EAAE,CAAEG,YAAY,EAAE,CAClBI,IAAI,CAAE,iBAAiB,CACvBY,QAAQ,CAAE,MAAM,CAChBJ,QAAQ,CAAGC,GAAG,EAAKA,GAAG,CAACT,IAAI,EAAI,EAAE,CACjCW,IAAI,CAAE,KAAK,CACXE,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,IACd,CAAC,CACD,CACErB,EAAE,CAAEG,YAAY,EAAE,CAClBI,IAAI,CAAE,KAAK,CACXY,QAAQ,CAAE,KAAK,CACfJ,QAAQ,CAAGC,GAAG,EAAKA,GAAG,CAACM,GAAG,EAAI,EAAE,CAChCJ,IAAI,CAAE,KAAK,CACXE,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,IACd,CAAC,CACD,CACErB,EAAE,CAAEG,YAAY,EAAE,CAClBI,IAAI,CAAE,aAAa,CACnBY,QAAQ,CAAE,aAAa,CACvBJ,QAAQ,CAAGC,GAAG,EAAKA,GAAG,CAACO,WAAW,EAAI,EAAE,CACxCL,IAAI,CAAE,KAAK,CACXE,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,IACd,CAAC,CAED,CACIrB,EAAE,CAAEG,YAAY,EAAE,CAClBI,IAAI,CAAE,YAAY,CAClBQ,QAAQ,CAAGC,GAAG,OAAAQ,YAAA,CAAAC,aAAA,OAAK,GAAG,EAAAD,YAAA,CAAAR,GAAG,CAACU,OAAO,UAAAF,YAAA,iBAAXA,YAAA,CAAaG,KAAK,GAAI,EAAE,IAAI,EAAAF,aAAA,CAAAT,GAAG,CAACU,OAAO,UAAAD,aAAA,iBAAXA,aAAA,CAAaG,KAAK,GAAI,EAAE,EAAE,GAC5ET,QAAQ,CAAE,YAAY,CACtBD,IAAI,CAAE,KAAK,CACXE,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,IAChB,CAAC,CACD,CACErB,EAAE,CAAEG,YAAY,EAAE,CAClBI,IAAI,CAAE,cAAc,CACpBQ,QAAQ,CAAGC,GAAG,EAAKxF,iBAAiB,CAACwF,GAAG,CAACa,UAAU,CAAC,CACpDV,QAAQ,CAAE,YAAY,CACtBD,IAAI,CAAE,KAAK,CACXE,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,IACd,CAAC,CACD,CACIrB,EAAE,CAAEG,YAAY,EAAE,CAClBI,IAAI,CAAE,cAAc,CACpBQ,QAAQ,CAAGC,GAAG,EAAKvF,kBAAkB,CAACuF,GAAG,CAACa,UAAU,CAAC,CACrDV,QAAQ,CAAE,YAAY,CACtBD,IAAI,CAAE,KAAK,CACXE,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KACd,CAAC,CACD,CACErB,EAAE,CAAEG,YAAY,EAAE,CAClBI,IAAI,CAAE,YAAY,CAClBQ,QAAQ,CAAGC,GAAG,OAAAc,YAAA,CAAAC,aAAA,OAAK,GAAG,EAAAD,YAAA,CAAAd,GAAG,CAACgB,OAAO,UAAAF,YAAA,iBAAXA,YAAA,CAAaH,KAAK,GAAI,EAAE,IAAI,EAAAI,aAAA,CAAAf,GAAG,CAACgB,OAAO,UAAAD,aAAA,iBAAXA,aAAA,CAAaH,KAAK,GAAI,EAAE,EAAE,GAC5ET,QAAQ,CAAE,YAAY,CACtBD,IAAI,CAAE,KAAK,CACXE,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,IACd,CAAC,CACH,CACErB,EAAE,CAAEG,YAAY,EAAE,CAClBI,IAAI,CAAE,cAAc,CACpBQ,QAAQ,CAAGC,GAAG,EAAKxF,iBAAiB,CAACwF,GAAG,CAACiB,UAAU,CAAC,CACpDd,QAAQ,CAAE,YAAY,CACtBD,IAAI,CAAE,KAAK,CACXE,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,IACd,CAAC,CACD,CACIrB,EAAE,CAAEG,YAAY,EAAE,CAClBI,IAAI,CAAE,cAAc,CACpBQ,QAAQ,CAAGC,GAAG,EAAKvF,kBAAkB,CAACuF,GAAG,CAACiB,UAAU,CAAC,CACrDd,QAAQ,CAAE,YAAY,CACtBD,IAAI,CAAE,KAAK,CACXE,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KAChB,CAAC,CACF,CAAC,CAEFnH,SAAS,CAAC,IAAM,CACd;AACAoG,UAAU,CAAE4B,WAAW,EAAK,CAC1B,GAAGA,WAAW,CAACzC,GAAG,CAAE0C,GAAG,EAAK,CAC1B,GAAIA,GAAG,CAAC5B,IAAI,GAAK,QAAQ,CAAE,CACzB;AACA,MAAO,CACL,GAAG4B,GAAG,CACNzB,IAAI,CAAGC,IAAI,eACT9E,KAAA,QAAK4E,SAAS,CAAC,qCAAqC,CAAAG,QAAA,eAClDjF,IAAA,WACE8E,SAAS,CAAC,uLAAuL,CACjMI,OAAO,CAAEA,CAAA,GAAM1D,WAAW,CAACwD,IAAI,CAAE,CAAAC,QAAA,cAEjCjF,IAAA,SAAM8E,SAAS,CAAC,mCAAmC,CAAAG,QAAA,CAAC,YAAU,CAAM,CAAC,CAC/D,CAAC,CACR,CAAAR,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEU,cAAc,gBAC9BnF,IAAA,WACE8E,SAAS,CAAC,mLAAmL,CAC7LI,OAAO,CAAEA,CAAA,GAAMd,UAAU,CAACY,IAAI,CAACX,EAAE,CAAE,CAAAY,QAAA,cAEnCjF,IAAA,SAAM8E,SAAS,CAAC,mCAAmC,CAAAG,QAAA,CAAC,aAAW,CAAM,CAAC,CAChE,CACT,CAEA,CAAAR,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEU,cAAc,gBAChCnF,IAAA,WACE8E,SAAS,CAAC,qLAAqL,CAC/LI,OAAO,CAAEA,CAAA,GAAMjB,UAAU,CAACe,IAAI,CAAE,CAAAC,QAAA,cAEhCjF,IAAA,SAAM8E,SAAS,CAAC,mCAAmC,CAAAG,QAAA,CAAC,cAAY,CAAM,CAAC,CACjE,CACP,CAEA,CAAAR,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEU,cAAc,gBAChCnF,IAAA,WACE8E,SAAS,CAAC,mLAAmL,CAC7LI,OAAO,CAAEA,CAAA,GAAMZ,YAAY,CAACU,IAAI,CAACX,EAAE,CAAE,CAAAY,QAAA,cAErCjF,IAAA,SAAM8E,SAAS,CAAC,mCAAmC,CAAAG,QAAA,CAAC,QAAM,CAAM,CAAC,CAC3D,CACP,EACE,CAET,CAAC,CACH,CACA,MAAO,CAAAuB,GAAG,CACZ,CAAC,CAAC,CACH,CAAC,CACJ,CAAC,CAAE,CAAC/B,eAAe,CAAC,CAAC,CAAE;AAIvB;AACA,KAAM,CAAAgC,SAAS,CAAGA,CAAA,GAAM,CACtB,GAAIrD,MAAM,CAACsD,IAAI,CAACjG,qBAAqB,CAAC,CAACkG,MAAM,CAAE,CAC7C,GAAI,CAAAC,MAAM,CAAG,CAAC,CAAC,CACfxD,MAAM,CAACsD,IAAI,CAACjG,qBAAqB,CAAC,CAACqD,GAAG,CAAEL,GAAG,EAAK,CAC9C,GAAI,MAAO,CAAAhD,qBAAqB,CAACgD,GAAG,CAAC,GAAK,QAAQ,CAAE,CAClDmD,MAAM,CAACnD,GAAG,CAAC,CAAG,EAAE,CAClB,CAAC,IAAM,CACLmD,MAAM,CAACnD,GAAG,CAAC,CAAG,EAAE,CAClB,CACF,CAAC,CAAC,CACF/C,wBAAwB,CAAC,CAAE,GAAGkG,MAAO,CAAC,CAAC,CACvC3D,gBAAgB,CAAC,CAAE,GAAG2D,MAAO,CAAC,CAAC,CACjC,CACAzE,cAAc,CAAC,CAAC,CAAC,CACnB,CAAC,CAGD;AACA,KAAM,CAAA0E,QAAQ,CAAG/H,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAgI,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF;AACA,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAF,QAAQ,CAC3BxH,aAAa,CAAC2H,SAAS,CAACC,iBAAiB,CAACC,QAAQ,CAAC,CACjD1E,OAAO,CAAEZ,UAAU,CACnBa,KAAK,CAAEX,aAAa,CACpBY,IAAI,CAAER,WAAW,CACjBS,QAAQ,CAAE,CAAAN,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE8E,KAAK,GAAI,EAAE,CAAE;AAClCvE,KAAK,CAAE/B,WACT,CAAC,CACH,CAAC,CAACuG,MAAM,CAAC,CAAC,CAAE;AAEZ,GAAI,EAACL,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEI,KAAK,GAAIJ,MAAM,CAACI,KAAK,CAAG,CAAC,CAAE,CACtC,MAAO,MAAK,CACd,CAEA,GAAI,CAAAE,EAAE,CAAG,CAAC,CAEV,GAAI,CAAAC,WAAW,CAAGP,MAAM,CAAC3E,IAAI,CAAC0B,GAAG,CAAEkB,IAAI,EAAK,CAC1C,GAAIN,OAAO,CAACiC,MAAM,CAAE,CAClB,GAAI,CAAAY,GAAG,CAAG,CAAC,CAAC,CACZ7C,OAAO,CAAC8C,OAAO,CAAEC,MAAM,EAAK,CAC1B,GAAI,CAACA,MAAM,CAAClC,IAAI,EAAIkC,MAAM,CAACrC,QAAQ,CAAE,CACnCmC,GAAG,CAACE,MAAM,CAAC7C,IAAI,CAAC,CAAG6C,MAAM,CAAC7C,IAAI,GAAK,MAAM,CAAGyC,EAAE,EAAE,CAAGI,MAAM,CAACrC,QAAQ,CAACJ,IAAI,CAAC,EAAI,EAAE,CAChF,CACF,CAAC,CAAC,CACF,MAAO,CAAAuC,GAAG,CACZ,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAG,SAAS,CAAGtI,IAAI,CAACuI,KAAK,CAACC,aAAa,CAACN,WAAW,CAAC,CACvD,KAAM,CAAAO,QAAQ,CAAGzI,IAAI,CAACuI,KAAK,CAACG,QAAQ,CAAC,CAAC,CACtC1I,IAAI,CAACuI,KAAK,CAACI,iBAAiB,CAACF,QAAQ,CAAEH,SAAS,CAAE,QAAQ,CAAC,CAE3D;AACA,KAAM,CAAAM,WAAW,CAAG5I,IAAI,CAAC6I,KAAK,CAACJ,QAAQ,CAAE,CACvCK,QAAQ,CAAE,MAAM,CAChBC,IAAI,CAAE,OACR,CAAC,CAAC,CACF,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACL,WAAW,CAAC,CAAE,CAAEG,IAAI,CAAE,0BAA2B,CAAC,CAAC,CAC1EhJ,MAAM,CAACiJ,IAAI,CAAE,GAAG/H,WAAW,CAACiI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAIhB,WAAW,CAACX,MAAM,OAAO,CAAC,CAC7E,CAAE,MAAOtF,KAAK,CAAE,CACdkH,OAAO,CAAClH,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CAAC,CAGD;AACF;AACA,KACE,KAAM,CAAAmH,2BAA2B,CAAGlK,WAAW,CAC7C,gBAKK,IAJH,CAAAmK,UAAU,CAAAC,SAAA,CAAA/B,MAAA,IAAA+B,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,CAAC,CAAC,IACf,CAAAP,IAAI,CAAAO,SAAA,CAAA/B,MAAA,IAAA+B,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,OAAO,IACd,CAAAE,SAAS,CAAAF,SAAA,CAAA/B,MAAA,IAAA+B,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,EAAE,IACd,CAAAG,SAAS,CAAAH,SAAA,CAAA/B,MAAA,IAAA+B,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,QAAQ,CAGpB,GAAI,CAAAI,YAAY,CAAGL,UAAU,CAACjD,QAAQ,EAAI,OAAO,CAEjD,GAAI,CACF5E,mBAAmB,CAACkI,YAAY,CAAC,CACjC5H,sBAAsB,CAAC,IAAI,CAAC,CAE5B,GAAI,CAAA4B,SAAS,CAAG,EAAE,CAElB,KAAM,CAAAiG,QAAQ,CAAG,KAAM,CAAAlG,oBAAoB,CAAC,CAAEsF,IAAI,CAAEA,IAAI,CAACa,IAAI,CAAC,CAAC,CAAEvB,MAAM,CAAEqB,YAAY,CAACE,IAAI,CAAC,CAAC,CAAEC,IAAI,CAAEL,SAAS,CAACI,IAAI,CAAC,CAAE,CAAC,CAAC,CAEvH,GAAID,QAAQ,CAAC3G,IAAI,CAAE,CACjBU,SAAS,CAAGiG,QAAQ,CAAC3G,IAAI,CAC3B,CAEA,GAAIU,SAAS,CAAC6D,MAAM,CAAE,CAEpB,GAAIkC,SAAS,GAAK,YAAY,CAAE,CAC9BrI,gBAAgB,CAAE0I,IAAI,GAAM,CAC1B,GAAGA,IAAI,CACP,CAACJ,YAAY,EAAGhG,SAClB,CAAC,CAAC,CAAC,CAEH,MAAO,CAAAA,SAAS,CAClB,CAEA,KAAM,CAAAqG,gBAAgB,CAAGrG,SAAS,CAC/BgB,GAAG,CAAEkB,IAAI,EAAK,CACb,GAAGyD,UAAU,CAACrD,QAAQ,CAAC,CACrB,GAAI,CAAAgE,KAAK,CAAGX,UAAU,CAACrD,QAAQ,CAACJ,IAAI,CAAC,CAErC,GAAGoE,KAAK,CAAC,CACP,GAAIpE,IAAI,CAACmC,KAAK,EAAInC,IAAI,CAACmC,KAAK,CAAG,CAAC,CAAE,CAChCiC,KAAK,EAAI,KAAKpE,IAAI,CAACmC,KAAK,GAAG,CAC7B,CAEA,MAAO,CAAEiC,KAAK,CAAE1F,KAAK,CAAEsB,IAAI,CAAC8D,YAAY,CAAE,CAAC,CAC7C,CAEF,MAAO,KAAI,CACX,CACF,CAAC,CAAC,CAACO,MAAM,CAACC,OAAO,CAAC,CAEpB9I,gBAAgB,CAAE0I,IAAI,GAAM,CAC1B,GAAGA,IAAI,CACP,CAACT,UAAU,CAACpE,EAAE,EAAGnF,WAAW,CAACiK,gBAAgB,CAC/C,CAAC,CAAC,CAAC,CAEH,MAAO,CAAAA,gBAAgB,CACzB,CACF,CAAE,MAAO9H,KAAK,CAAE,CACdC,QAAQ,CAACD,KAAK,CAACkI,OAAO,CAAC,CACzB,CAAC,OAAS,CACRrI,sBAAsB,CAAC,KAAK,CAAC,CAC/B,CACF,CAAC,CACD,EACF,CAAC,CAED,mBACElB,IAAA,YAAS8E,SAAS,CAAC,+DAA+D,CAAAG,QAAA,cAChF/E,KAAA,QAAK4E,SAAS,CAAC,eAAe,CAAAG,QAAA,eAE5B/E,KAAA,QAAK4E,SAAS,CAAC,gGAAgG,CAAAG,QAAA,eAC7GjF,IAAA,QAAK8E,SAAS,CAAC,8BAA8B,CAAAG,QAAA,cAC3CjF,IAAA,OAAI8E,SAAS,CAAC,qBAAqB,CAAAG,QAAA,CAAE5E,WAAW,CAAK,CAAC,CACnD,CAAC,cACNH,KAAA,QAAK4E,SAAS,CAAC,yCAAyC,CAAAG,QAAA,eAEtDjF,IAAA,CAACrB,aAAa,EAAC+F,OAAO,CAAEA,OAAQ,CAACC,UAAU,CAAEA,UAAW,CAAE,CAAC,CAGzD,CAACrC,UAAU,EAAID,SAAS,EAAImH,QAAQ,CAACnH,SAAS,CAAC8E,KAAK,CAAC,CAAG,CAAC,eACzDnH,IAAA,CAAAI,SAAA,EAAA6E,QAAA,cACE/E,KAAA,WACE4E,SAAS,CAAC,mZAAmZ,CAC7ZI,OAAO,CAAE4B,aAAc,CAAA7B,QAAA,EAEtB3C,UAAU,eACTtC,IAAA,CAAAI,SAAA,EAAA6E,QAAA,cACEjF,IAAA,SAAM8E,SAAS,CAAC,qDAAqD,CAAAG,QAAA,CAAC,mBAEtE,CAAM,CAAC,CACP,CACH,CACA,CAAC3C,UAAU,eACVtC,IAAA,SAAM8E,SAAS,CAAC,wCAAwC,CAAAG,QAAA,CAAC,aAEzD,CAAM,CACP,CAAC,mBACe,CAAC5C,SAAS,CAAC8E,KAAK,CAAC,GACpC,EAAQ,CAAC,CACT,CACH,CAEA1C,eAAe,CAACU,cAAc,eAC7BnF,IAAA,WACE8E,SAAS,CAAC,+XAA+X,CAEzYI,OAAO,CAAEA,CAAA,GAAMvD,kBAAkB,CAAC,IAAI,CAAE,CAAAsD,QAAA,CACzC,SAED,CAAQ,CACT,EACE,CAAC,EACH,CAAC,cAGNjF,IAAA,CAACpB,YAAY,EACT8F,OAAO,CAAEA,OAAQ,CACjBjE,qBAAqB,CAAEA,qBAAsB,CAC7CC,wBAAwB,CAAEA,wBAAyB,CACnD8H,2BAA2B,CAAEA,2BAA4B,CACzDjI,aAAa,CAAEA,aAAc,CAC7BU,mBAAmB,CAAEA,mBAAoB,CACzCN,gBAAgB,CAAEA,gBAAiB,CACnC8F,SAAS,CAAEA,SAAU,CACrBtE,cAAc,CAAEA,cAAe,CAC/Bc,gBAAgB,CAAEA,gBAAiB,CACtC,CAAC,CAGDV,UAAU,eAAIvC,IAAA,QAAK8E,SAAS,CAAC,cAAc,CAAAG,QAAA,CAAE5D,KAAK,CAAM,CAAC,CAEzDiB,UAAU,eAAItC,IAAA,CAACvB,OAAO,GAAE,CAAC,cAK1BuB,IAAA,QAAK8E,SAAS,CAAC,kDAAkD,CAAAG,QAAA,cAC/DjF,IAAA,CAACxB,SAAS,EACRkG,OAAO,CAAEA,OAAQ,CACjBtC,IAAI,CAAE,CAAAC,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAED,IAAI,GAAI,EAAG,CAC5B0C,SAAS,CAAC,6BAA6B,CACvC2E,WAAW,MAEXC,gBAAgB,MAChBC,UAAU,MACVC,UAAU,MACVC,gBAAgB,MAChBC,iBAAiB,CAAE9H,OAAQ,CAC3B+H,mBAAmB,CAAE,CAAA1H,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE8E,KAAK,GAAI,CAAE,CAC3C6C,YAAY,CAAGtH,IAAI,EAAK,CACtB,GAAIA,IAAI,GAAKR,WAAW,CAAE,CACxBC,cAAc,CAACO,IAAI,CAAC,CACtB,CACF,CAAE,CACFuH,mBAAmB,CAAGC,UAAU,EAAK,CACnC,GAAGA,UAAU,GAAKlI,OAAO,CAAC,CACxBC,UAAU,CAACiI,UAAU,CAAC,CACtB/H,cAAc,CAAC,CAAC,CAAC,CACnB,CACF,CAAE,CACFgI,0BAA0B,CAAE,CAC1BC,iBAAiB,CAAE,IAAI,CACvBC,qBAAqB,CAAE,KACzB,CAAE,CACFC,UAAU,MACVC,MAAM,CAAE,QAAAA,CAAC9C,MAAM,CAA2B,IAAzB,CAAA3F,aAAa,CAAA4G,SAAA,CAAA/B,MAAA,IAAA+B,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAC,MAAM,CACnC,GAAGtF,MAAM,CAACsD,IAAI,CAACe,MAAM,CAAC,CAACd,MAAM,CAAC,CAC5B9E,aAAa,CAAC4F,MAAM,CAACjC,QAAQ,EAAIiC,MAAM,CAAC7C,IAAI,EAAI,YAAY,CAAC,CAC7D7C,gBAAgB,CAACD,aAAa,EAAI,MAAM,CAAC,CAC3C,CACF,CAAE,CACH,CAAC,CACC,CAAC,CAGLJ,eAAe,eACZ1B,IAAA,CAACL,aAAa,EACV6K,SAAS,CAAE9I,eAAgB,CAC3B+I,UAAU,CAAE9I,kBAAmB,CAClC,CACJ,CAGAZ,YAAY,eACXf,IAAA,CAACN,cAAc,EACb8K,SAAS,CAAEzJ,YAAa,CACxB0J,UAAU,CAAEzJ,eAAgB,CAC5BG,WAAW,CAAEA,WAAY,CAC1B,CACF,CAEAI,QAAQ,eACP;AACAvB,IAAA,CAACnB,SAAS,EAACmG,IAAI,CAAEzD,QAAS,CAACC,WAAW,CAAEA,WAAY,CAACkD,OAAO,CAAEA,OAAQ,CAACN,UAAU,CAAEA,UAAW,CAACE,YAAY,CAAEA,YAAa,CAAE,CAC7H,EAEE,CAAC,CACC,CAAC,CAEd,CAAC,CAGD,cAAe,CAAAhE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}