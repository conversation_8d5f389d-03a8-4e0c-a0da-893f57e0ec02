{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import Loading from\"../common/Loading\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const normalizeShift=(item,idx)=>{var _ref,_ref2,_ref3,_stats$designer,_ref4,_ref5,_ref6,_stats$developer,_ref7,_ref8,_ref9,_stats$qa;const name=(item===null||item===void 0?void 0:item.name)||(item===null||item===void 0?void 0:item.shift)||(item===null||item===void 0?void 0:item.title)||[\"Morning\",\"Evening\",\"Night\"][idx%3];const stats=(item===null||item===void 0?void 0:item.stats)||(item===null||item===void 0?void 0:item.counts)||item||{};return{name,designer:(_ref=(_ref2=(_ref3=(_stats$designer=stats===null||stats===void 0?void 0:stats.designer)!==null&&_stats$designer!==void 0?_stats$designer:stats===null||stats===void 0?void 0:stats.designers)!==null&&_ref3!==void 0?_ref3:stats===null||stats===void 0?void 0:stats.designer_count)!==null&&_ref2!==void 0?_ref2:stats===null||stats===void 0?void 0:stats.total_designer)!==null&&_ref!==void 0?_ref:0,developer:(_ref4=(_ref5=(_ref6=(_stats$developer=stats===null||stats===void 0?void 0:stats.developer)!==null&&_stats$developer!==void 0?_stats$developer:stats===null||stats===void 0?void 0:stats.developers)!==null&&_ref6!==void 0?_ref6:stats===null||stats===void 0?void 0:stats.developer_count)!==null&&_ref5!==void 0?_ref5:stats===null||stats===void 0?void 0:stats.total_developer)!==null&&_ref4!==void 0?_ref4:0,qa:(_ref7=(_ref8=(_ref9=(_stats$qa=stats===null||stats===void 0?void 0:stats.qa)!==null&&_stats$qa!==void 0?_stats$qa:stats===null||stats===void 0?void 0:stats.qa_count)!==null&&_ref9!==void 0?_ref9:stats===null||stats===void 0?void 0:stats.quality_assurance)!==null&&_ref8!==void 0?_ref8:stats===null||stats===void 0?void 0:stats.total_qa)!==null&&_ref7!==void 0?_ref7:0};};const SmallStat=_ref10=>{let{label,value}=_ref10;return/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 px-4 py-3 flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-700 dark:text-gray-200\",children:label}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 text-gray-900 dark:text-gray-100\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDC64\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:String(value).padStart(2,\"0\")})]})]});};const ShiftCard=_ref11=>{let{shift}=_ref11;return/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3\",children:[shift.name,\" Shift\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-3 gap-3\",children:[/*#__PURE__*/_jsx(SmallStat,{label:\"Total Designer\",value:shift.designer}),/*#__PURE__*/_jsx(SmallStat,{label:\"Total Developer\",value:shift.developer}),/*#__PURE__*/_jsx(SmallStat,{label:\"Total QA\",value:shift.qa})]})]});};const ShiftSummarySection=()=>{const[shifts,setShifts]=useState([]);const[loading,setLoading]=useState(true);const[err,setErr]=useState(null);useEffect(()=>{const token=localStorage.getItem(\"token\");if(!token){setErr(\"No auth token found\");setLoading(false);return;}const load=async()=>{setLoading(true);try{const mockShifts=[{name:\"Evening\",stats:{designer:20,developer:25,qa:6}},{name:\"Morning\",stats:{designer:20,developer:25,qa:6}},{name:\"Night\",stats:{designer:20,developer:25,qa:6}}];const normalized=mockShifts.map((item,idx)=>normalizeShift(item,idx));const order=[\"evening\",\"morning\",\"night\"];normalized.sort((a,b)=>order.indexOf((a.name||\"\").toLowerCase())-order.indexOf((b.name||\"\").toLowerCase()));setShifts(normalized);}catch(error){console.error('Error:',error);setErr('Unable to load shift data');}finally{setLoading(false);}};load();},[]);if(loading)return/*#__PURE__*/_jsx(Loading,{});if(err)return/*#__PURE__*/_jsx(\"div\",{className:\"text-red-500\",children:err});if(!shifts.length)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-4\",children:shifts.map((s,i)=>/*#__PURE__*/_jsx(ShiftCard,{shift:s},`${s.name}-${i}`))});};export default ShiftSummarySection;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Loading", "jsx", "_jsx", "jsxs", "_jsxs", "normalizeShift", "item", "idx", "_ref", "_ref2", "_ref3", "_stats$designer", "_ref4", "_ref5", "_ref6", "_stats$developer", "_ref7", "_ref8", "_ref9", "_stats$qa", "name", "shift", "title", "stats", "counts", "designer", "designers", "designer_count", "total_designer", "developer", "developers", "developer_count", "total_developer", "qa", "qa_count", "quality_assurance", "total_qa", "SmallStat", "_ref10", "label", "value", "className", "children", "String", "padStart", "ShiftCard", "_ref11", "ShiftSummarySection", "shifts", "setShifts", "loading", "setLoading", "err", "setErr", "token", "localStorage", "getItem", "load", "mockShifts", "normalized", "map", "order", "sort", "a", "b", "indexOf", "toLowerCase", "error", "console", "length", "s", "i"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/ShiftSummarySection.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport Loading from \"../common/Loading\";\r\n\r\nconst normalizeShift = (item, idx) => {\r\n  const name = item?.name || item?.shift || item?.title || [\"Morning\", \"Evening\", \"Night\"][idx % 3];\r\n  const stats = item?.stats || item?.counts || item || {};\r\n  return {\r\n    name,\r\n    designer:\r\n      stats?.designer ?? stats?.designers ?? stats?.designer_count ?? stats?.total_designer ?? 0,\r\n    developer:\r\n      stats?.developer ?? stats?.developers ?? stats?.developer_count ?? stats?.total_developer ?? 0,\r\n    qa: stats?.qa ?? stats?.qa_count ?? stats?.quality_assurance ?? stats?.total_qa ?? 0,\r\n  };\r\n};\r\n\r\nconst SmallStat = ({ label, value }) => (\r\n  <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 px-4 py-3 flex items-center justify-between\">\r\n    <div className=\"text-sm font-medium text-gray-700 dark:text-gray-200\">{label}</div>\r\n    <div className=\"flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n      <span>👤</span>\r\n      <span className=\"font-semibold\">{String(value).padStart(2, \"0\")}</span>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ShiftCard = ({ shift }) => (\r\n  <div className=\"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4\">\r\n    <h4 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3\">{shift.name} Shift</h4>\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3\">\r\n      <SmallStat label=\"Total Designer\" value={shift.designer} />\r\n      <SmallStat label=\"Total Developer\" value={shift.developer} />\r\n      <SmallStat label=\"Total QA\" value={shift.qa} />\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ShiftSummarySection = () => {\r\n  const [shifts, setShifts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [err, setErr] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (!token) {\r\n      setErr(\"No auth token found\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n    const load = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const mockShifts = [\r\n          {\r\n            name: \"Evening\",\r\n            stats: {\r\n              designer: 20,\r\n              developer: 25,\r\n              qa: 6\r\n            }\r\n          },\r\n          {\r\n            name: \"Morning\",\r\n            stats: {\r\n              designer: 20,\r\n              developer: 25,\r\n              qa: 6\r\n            }\r\n          },\r\n          {\r\n            name: \"Night\",\r\n            stats: {\r\n              designer: 20,\r\n              developer: 25,\r\n              qa: 6\r\n            }\r\n          }\r\n        ];\r\n\r\n        const normalized = mockShifts.map((item, idx) => normalizeShift(item, idx));\r\n        const order = [\"evening\", \"morning\", \"night\"];\r\n        normalized.sort(\r\n          (a, b) => order.indexOf((a.name || \"\").toLowerCase()) - order.indexOf((b.name || \"\").toLowerCase())\r\n        );\r\n        setShifts(normalized);\r\n      } catch (error) {\r\n        console.error('Error:', error);\r\n        setErr('Unable to load shift data');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    load();\r\n  }, []);\r\n\r\n  if (loading) return <Loading />;\r\n  if (err) return <div className=\"text-red-500\">{err}</div>;\r\n  if (!shifts.length) return null;\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\r\n      {shifts.map((s, i) => (\r\n        <ShiftCard key={`${s.name}-${i}`} shift={s} />\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ShiftSummarySection;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,OAAO,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,cAAc,CAAGA,CAACC,IAAI,CAAEC,GAAG,GAAK,KAAAC,IAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,SAAA,CACpC,KAAM,CAAAC,IAAI,CAAG,CAAAd,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEc,IAAI,IAAId,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEe,KAAK,IAAIf,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEgB,KAAK,GAAI,CAAC,SAAS,CAAE,SAAS,CAAE,OAAO,CAAC,CAACf,GAAG,CAAG,CAAC,CAAC,CACjG,KAAM,CAAAgB,KAAK,CAAG,CAAAjB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiB,KAAK,IAAIjB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEkB,MAAM,GAAIlB,IAAI,EAAI,CAAC,CAAC,CACvD,MAAO,CACLc,IAAI,CACJK,QAAQ,EAAAjB,IAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,eAAA,CACNY,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEE,QAAQ,UAAAd,eAAA,UAAAA,eAAA,CAAIY,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEG,SAAS,UAAAhB,KAAA,UAAAA,KAAA,CAAIa,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEI,cAAc,UAAAlB,KAAA,UAAAA,KAAA,CAAIc,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEK,cAAc,UAAApB,IAAA,UAAAA,IAAA,CAAI,CAAC,CAC5FqB,SAAS,EAAAjB,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,gBAAA,CACPQ,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEM,SAAS,UAAAd,gBAAA,UAAAA,gBAAA,CAAIQ,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEO,UAAU,UAAAhB,KAAA,UAAAA,KAAA,CAAIS,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEQ,eAAe,UAAAlB,KAAA,UAAAA,KAAA,CAAIU,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAES,eAAe,UAAApB,KAAA,UAAAA,KAAA,CAAI,CAAC,CAChGqB,EAAE,EAAAjB,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,SAAA,CAAEI,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEU,EAAE,UAAAd,SAAA,UAAAA,SAAA,CAAII,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEW,QAAQ,UAAAhB,KAAA,UAAAA,KAAA,CAAIK,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEY,iBAAiB,UAAAlB,KAAA,UAAAA,KAAA,CAAIM,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEa,QAAQ,UAAApB,KAAA,UAAAA,KAAA,CAAI,CACrF,CAAC,CACH,CAAC,CAED,KAAM,CAAAqB,SAAS,CAAGC,MAAA,MAAC,CAAEC,KAAK,CAAEC,KAAM,CAAC,CAAAF,MAAA,oBACjClC,KAAA,QAAKqC,SAAS,CAAC,mIAAmI,CAAAC,QAAA,eAChJxC,IAAA,QAAKuC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAAEH,KAAK,CAAM,CAAC,cACnFnC,KAAA,QAAKqC,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvExC,IAAA,SAAAwC,QAAA,CAAM,cAAE,CAAM,CAAC,cACfxC,IAAA,SAAMuC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEC,MAAM,CAACH,KAAK,CAAC,CAACI,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAAO,CAAC,EACpE,CAAC,EACH,CAAC,EACP,CAED,KAAM,CAAAC,SAAS,CAAGC,MAAA,MAAC,CAAEzB,KAAM,CAAC,CAAAyB,MAAA,oBAC1B1C,KAAA,QAAKqC,SAAS,CAAC,uFAAuF,CAAAC,QAAA,eACpGtC,KAAA,OAAIqC,SAAS,CAAC,6DAA6D,CAAAC,QAAA,EAAErB,KAAK,CAACD,IAAI,CAAC,QAAM,EAAI,CAAC,cACnGhB,KAAA,QAAKqC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDxC,IAAA,CAACmC,SAAS,EAACE,KAAK,CAAC,gBAAgB,CAACC,KAAK,CAAEnB,KAAK,CAACI,QAAS,CAAE,CAAC,cAC3DvB,IAAA,CAACmC,SAAS,EAACE,KAAK,CAAC,iBAAiB,CAACC,KAAK,CAAEnB,KAAK,CAACQ,SAAU,CAAE,CAAC,cAC7D3B,IAAA,CAACmC,SAAS,EAACE,KAAK,CAAC,UAAU,CAACC,KAAK,CAAEnB,KAAK,CAACY,EAAG,CAAE,CAAC,EAC5C,CAAC,EACH,CAAC,EACP,CAED,KAAM,CAAAc,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACmD,OAAO,CAAEC,UAAU,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqD,GAAG,CAAEC,MAAM,CAAC,CAAGtD,QAAQ,CAAC,IAAI,CAAC,CAEpCD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwD,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAI,CAACF,KAAK,CAAE,CACVD,MAAM,CAAC,qBAAqB,CAAC,CAC7BF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACA,KAAM,CAAAM,IAAI,CAAG,KAAAA,CAAA,GAAY,CACvBN,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAO,UAAU,CAAG,CACjB,CACEtC,IAAI,CAAE,SAAS,CACfG,KAAK,CAAE,CACLE,QAAQ,CAAE,EAAE,CACZI,SAAS,CAAE,EAAE,CACbI,EAAE,CAAE,CACN,CACF,CAAC,CACD,CACEb,IAAI,CAAE,SAAS,CACfG,KAAK,CAAE,CACLE,QAAQ,CAAE,EAAE,CACZI,SAAS,CAAE,EAAE,CACbI,EAAE,CAAE,CACN,CACF,CAAC,CACD,CACEb,IAAI,CAAE,OAAO,CACbG,KAAK,CAAE,CACLE,QAAQ,CAAE,EAAE,CACZI,SAAS,CAAE,EAAE,CACbI,EAAE,CAAE,CACN,CACF,CAAC,CACF,CAED,KAAM,CAAA0B,UAAU,CAAGD,UAAU,CAACE,GAAG,CAAC,CAACtD,IAAI,CAAEC,GAAG,GAAKF,cAAc,CAACC,IAAI,CAAEC,GAAG,CAAC,CAAC,CAC3E,KAAM,CAAAsD,KAAK,CAAG,CAAC,SAAS,CAAE,SAAS,CAAE,OAAO,CAAC,CAC7CF,UAAU,CAACG,IAAI,CACb,CAACC,CAAC,CAAEC,CAAC,GAAKH,KAAK,CAACI,OAAO,CAAC,CAACF,CAAC,CAAC3C,IAAI,EAAI,EAAE,EAAE8C,WAAW,CAAC,CAAC,CAAC,CAAGL,KAAK,CAACI,OAAO,CAAC,CAACD,CAAC,CAAC5C,IAAI,EAAI,EAAE,EAAE8C,WAAW,CAAC,CAAC,CACpG,CAAC,CACDjB,SAAS,CAACU,UAAU,CAAC,CACvB,CAAE,MAAOQ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,CAAEA,KAAK,CAAC,CAC9Bd,MAAM,CAAC,2BAA2B,CAAC,CACrC,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CACDM,IAAI,CAAC,CAAC,CACR,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIP,OAAO,CAAE,mBAAOhD,IAAA,CAACF,OAAO,GAAE,CAAC,CAC/B,GAAIoD,GAAG,CAAE,mBAAOlD,IAAA,QAAKuC,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEU,GAAG,CAAM,CAAC,CACzD,GAAI,CAACJ,MAAM,CAACqB,MAAM,CAAE,MAAO,KAAI,CAE/B,mBACEnE,IAAA,QAAKuC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDM,MAAM,CAACY,GAAG,CAAC,CAACU,CAAC,CAAEC,CAAC,gBACfrE,IAAA,CAAC2C,SAAS,EAAwBxB,KAAK,CAAEiD,CAAE,EAA3B,GAAGA,CAAC,CAAClD,IAAI,IAAImD,CAAC,EAAe,CAC9C,CAAC,CACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}