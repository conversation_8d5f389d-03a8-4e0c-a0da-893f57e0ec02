{"__meta": {"id": "Xaf6a6149acb83af652d9e720b9d32887", "datetime": "2025-08-11 14:18:42", "utime": **********.168023, "method": "GET", "uri": "/api/users", "ip": "127.0.0.1"}, "php": {"version": "8.0.30", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:18:42] LOG.info: Authenticated user roles: [\n    \"super-admin\"\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.114113, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754900321.622834, "end": **********.168036, "duration": 0.5452020168304443, "duration_str": "545ms", "measures": [{"label": "Booting", "start": 1754900321.622834, "relative_start": 0, "end": **********.078793, "relative_end": **********.078793, "duration": 0.4559590816497803, "duration_str": "456ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.0788, "relative_start": 0.4559659957885742, "end": **********.168038, "relative_end": 1.9073486328125e-06, "duration": 0.08923792839050293, "duration_str": "89.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24353720, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/users", "middleware": "api, auth:sanctum, cors, verified, role:super-admin|admin", "controller": "App\\Http\\Controllers\\AuthController@index", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=43\" onclick=\"\">app/Http/Controllers/AuthController.php:43-63</a>"}, "queries": {"nb_statements": 20, "nb_visible_statements": 21, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00873, "accumulated_duration_str": "8.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.095138, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '433' limit 1", "type": "query", "params": [], "bindings": ["433"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.099071, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 20.389}, {"sql": "select * from `users` where `users`.`id` = 150 limit 1", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.104372, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "creative_app3", "explain": null, "start_percent": 20.389, "width_percent": 3.666}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-08-11 14:18:42', `personal_access_tokens`.`updated_at` = '2025-08-11 14:18:42' where `id` = 433", "type": "query", "params": [], "bindings": ["2025-08-11 14:18:42", "2025-08-11 14:18:42", 433], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.10624, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "creative_app3", "explain": null, "start_percent": 24.055, "width_percent": 13.746}, {"sql": "select `name` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 20, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": **********.1121292, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "role:18", "source": {"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=18", "ajax": false, "filename": "RoleMiddleware.php", "line": "18"}, "connection": "creative_app3", "explain": null, "start_percent": 37.801, "width_percent": 3.093}, {"sql": "select exists(select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150 and `name` in ('super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member', 'guest')) as `exists`", "type": "query", "params": [], "bindings": [150, "super-admin", "admin", "hod", "manager", "team-lead", "coordinator", "shift-lead", "team-member", "guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 15, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": **********.114355, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "role:21", "source": {"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=21", "ajax": false, "filename": "RoleMiddleware.php", "line": "21"}, "connection": "creative_app3", "explain": null, "start_percent": 40.893, "width_percent": 6.071}, {"sql": "select * from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.116052, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 46.964, "width_percent": 4.467}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.117879, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 51.432, "width_percent": 4.009}, {"sql": "select `teams`.*, `team_user`.`user_id` as `pivot_user_id`, `team_user`.`team_id` as `pivot_team_id`, `team_user`.`is_default` as `pivot_is_default` from `teams` inner join `team_user` on `teams`.`id` = `team_user`.`team_id` where `team_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.120122, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 55.441, "width_percent": 4.009}, {"sql": "select `departments`.*, `department_user`.`user_id` as `pivot_user_id`, `department_user`.`department_id` as `pivot_department_id` from `departments` inner join `department_user` on `departments`.`id` = `department_user`.`department_id` where `department_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.122006, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 59.45, "width_percent": 2.749}, {"sql": "select `resource_statuses`.*, `resource_status_user`.`user_id` as `pivot_user_id`, `resource_status_user`.`resource_status_id` as `pivot_resource_status_id` from `resource_statuses` inner join `resource_status_user` on `resource_statuses`.`id` = `resource_status_user`.`resource_status_id` where `resource_status_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.123677, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 62.199, "width_percent": 3.666}, {"sql": "select `resource_types`.*, `resource_type_user`.`user_id` as `pivot_user_id`, `resource_type_user`.`resource_type_id` as `pivot_resource_type_id` from `resource_types` inner join `resource_type_user` on `resource_types`.`id` = `resource_type_user`.`resource_type_id` where `resource_type_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.12543, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 65.865, "width_percent": 3.093}, {"sql": "select `billing_statuses`.*, `billing_status_user`.`user_id` as `pivot_user_id`, `billing_status_user`.`billing_status_id` as `pivot_billing_status_id` from `billing_statuses` inner join `billing_status_user` on `billing_statuses`.`id` = `billing_status_user`.`billing_status_id` where `billing_status_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.12716, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 68.958, "width_percent": 3.322}, {"sql": "select `designations`.*, `designation_user`.`user_id` as `pivot_user_id`, `designation_user`.`designation_id` as `pivot_designation_id` from `designations` inner join `designation_user` on `designations`.`id` = `designation_user`.`designation_id` where `designation_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.128898, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 72.279, "width_percent": 2.978}, {"sql": "select `bloods`.*, `blood_user`.`user_id` as `pivot_user_id`, `blood_user`.`blood_id` as `pivot_blood_id` from `bloods` inner join `blood_user` on `bloods`.`id` = `blood_user`.`blood_id` where `blood_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.130941, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 75.258, "width_percent": 4.124}, {"sql": "select `branches`.*, `branch_user`.`user_id` as `pivot_user_id`, `branch_user`.`branch_id` as `pivot_branch_id` from `branches` inner join `branch_user` on `branches`.`id` = `branch_user`.`branch_id` where `branch_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.132921, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 79.381, "width_percent": 5.269}, {"sql": "select `locations`.*, `branch_location`.`branch_id` as `pivot_branch_id`, `branch_location`.`location_id` as `pivot_location_id` from `locations` inner join `branch_location` on `locations`.`id` = `branch_location`.`location_id` where `branch_location`.`branch_id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1348808, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 84.651, "width_percent": 2.978}, {"sql": "select `contact_types`.*, `contact_type_user`.`user_id` as `pivot_user_id`, `contact_type_user`.`contact_type_id` as `pivot_contact_type_id` from `contact_types` inner join `contact_type_user` on `contact_types`.`id` = `contact_type_user`.`contact_type_id` where `contact_type_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1364372, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 87.629, "width_percent": 3.322}, {"sql": "select `member_statuses`.*, `member_status_user`.`user_id` as `pivot_user_id`, `member_status_user`.`member_status_id` as `pivot_member_status_id` from `member_statuses` inner join `member_status_user` on `member_statuses`.`id` = `member_status_user`.`member_status_id` where `member_status_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1381428, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 90.951, "width_percent": 2.978}, {"sql": "select `available_statuses`.*, `available_status_user`.`user_id` as `pivot_user_id`, `available_status_user`.`available_status_id` as `pivot_available_status_id` from `available_statuses` inner join `available_status_user` on `available_statuses`.`id` = `available_status_user`.`available_status_id` where `available_status_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.139818, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 93.929, "width_percent": 2.864}, {"sql": "select `onsite_statuses`.*, `onsite_status_user`.`user_id` as `pivot_user_id`, `onsite_status_user`.`onsite_status_id` as `pivot_onsite_status_id` from `onsite_statuses` inner join `onsite_status_user` on `onsite_statuses`.`id` = `onsite_status_user`.`onsite_status_id` where `onsite_status_user`.`user_id` in (67, 83, 134, 135, 139, 143, 144, 145, 147, 148, 149, 150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.141499, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "AuthController.php:60", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/AuthController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\AuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FAuthController.php&line=60", "ajax": false, "filename": "AuthController.php", "line": "60"}, "connection": "creative_app3", "explain": null, "start_percent": 96.793, "width_percent": 3.207}]}, "models": {"data": {"App\\Models\\Team": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "App\\Models\\User": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Department": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\Resource_status": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FResource_status.php&line=1", "ajax": false, "filename": "Resource_status.php", "line": "?"}}, "App\\Models\\Resource_type": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FResource_type.php&line=1", "ajax": false, "filename": "Resource_type.php", "line": "?"}}, "App\\Models\\Billing_status": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FBilling_status.php&line=1", "ajax": false, "filename": "Billing_status.php", "line": "?"}}, "App\\Models\\Designation": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FDesignation.php&line=1", "ajax": false, "filename": "Designation.php", "line": "?"}}, "App\\Models\\Branch": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FBranch.php&line=1", "ajax": false, "filename": "Branch.php", "line": "?"}}, "App\\Models\\ContactType": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FContactType.php&line=1", "ajax": false, "filename": "ContactType.php", "line": "?"}}, "App\\Models\\MemberStatus": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FMemberStatus.php&line=1", "ajax": false, "filename": "MemberStatus.php", "line": "?"}}, "App\\Models\\AvailableStatus": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FAvailableStatus.php&line=1", "ajax": false, "filename": "AvailableStatus.php", "line": "?"}}, "App\\Models\\OnsiteStatus": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FOnsiteStatus.php&line=1", "ajax": false, "filename": "OnsiteStatus.php", "line": "?"}}, "App\\Models\\Blood": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FBlood.php&line=1", "ajax": false, "filename": "Blood.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\Location": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FLocation.php&line=1", "ajax": false, "filename": "Location.php", "line": "?"}}}, "count": 175, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/users", "status_code": "<pre class=sf-dump id=sf-dump-2006990129 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2006990129\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1813391994 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1813391994\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-566159044 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-566159044\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1820631546 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 433|P******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820631546\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1084350092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1084350092\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1339968077 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 11 Aug 2025 08:18:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">GET, POST, PUT, DELETE, OPTIONS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Content-Type, Authorization, X-Requested-With</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">147</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339968077\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-785361080 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-785361080\", {\"maxDepth\":0})</script>\n"}}