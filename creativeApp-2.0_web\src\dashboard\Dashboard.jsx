import React from "react";
import WeatherData from "../pages/weatherAndTime/WeatherData";
import { API_URL } from "../common/fetchData/apiConfig";
import { useNavigate } from "react-router-dom";
import Loading from "../common/Loading";

import WelcomeCard from "./WelcomeCard";
import ClientTeamsSection from "./ClientTeamsSection";
import ShiftSummarySection from "./ShiftSummarySection";

const isTokenValid = () => {
  const token = localStorage.getItem("token");
  return token !== null && token !== "";
};

const Dashboard = () => {
  const [userData, setUserData] = React.useState(null);
  const [error, setError] = React.useState(null);
  const [filterOptionLoading, setFilterOptionLoading] = React.useState(false);
  const navigate = useNavigate();

  // Date/time strings (can be from backend/global; using static for now)
  const [dateTimeStrings] = React.useState({
    english: "Wednesday, 13 November, 2024, Late Autumn",
    bengali: "বুধবার, ২৮শে কার্তিক, ১৪৩১ বঙ্গাব্দ, হেমন্ত ঋতু",
    hijri: "Al-arbi 'aa', 11 Jumada 1446 Hijri, Awakhirul Kharif",
  });

  React.useEffect(() => {
    const token = localStorage.getItem("token");

    if (!isTokenValid()) {
      setError("No valid authentication token found.");
      setFilterOptionLoading(false);
      navigate("/login");
      return;
    }

    const user = localStorage.getItem("user");
    if (user) {
      setUserData(JSON.parse(user));
      return;
    }

    const fetchUserData = async () => {
      setFilterOptionLoading(true);
      try {
        const response = await fetch(`${API_URL}/logged-users`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) throw new Error("Failed to fetch user data");
        const data = await response.json();
        setUserData(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setFilterOptionLoading(false);
      }
    };

    fetchUserData();
  }, [navigate]);

  if (filterOptionLoading) return <Loading />;

  return (
    <div className="rounded-xl">
      {/* Top row: Welcome + Weather */}
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12 lg:col-span-7">
          <WelcomeCard userData={userData} dateTimeStrings={dateTimeStrings} />
        </div>
        <div className="col-span-12 lg:col-span-5 flex flex-wrap border border-gray-300 dark:border-gray-600 rounded-2xl">
          <WeatherData />
        </div>
      </div>

      {/* Clients/Teams grid */}
      <div className="mt-4">
        <ClientTeamsSection />
      </div>

      {/* Shifts summary */}
      <div className="mt-4">
        <ShiftSummarySection />
      </div>

      {error && <div className="mt-4 text-red-500">{error}</div>}
    </div>
  );
};

export default Dashboard;