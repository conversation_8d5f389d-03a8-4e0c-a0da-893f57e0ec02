"use strict";(self.webpackChunkcreativeapp=self.webpackChunkcreativeapp||[]).push([[352],{19352:(e,t,a)=>{a.r(t),a.d(t,{default:()=>j});var s=a(65043),r=a(58786),l=a(13076),i=a(82949),n=a(83003),o=a(47554),d=a(72450),c=a(11238),u=a(64398),m=a(73216),g=a(70579);const x="https://creativeapp.sebpo.net/banner/test/backend/public/api",p=e=>{let{isVisible:t,setVisible:a,dataItemsId:r}=e;const[l,n]=(0,s.useState)(""),[o,d]=(0,s.useState)(""),[c,u]=(0,s.useState)(""),[m,p]=(0,s.useState)(null);(0,s.useEffect)((()=>{const e=localStorage.getItem("user_id");e&&p(e)}),[]),(0,s.useEffect)((()=>{(async()=>{if(!r)return;const e=localStorage.getItem("token");if(e)try{const t=await fetch(`${x}/billing_statuses`,{method:"GET",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to fetch billing statuses: "+t.statusText);const a=(await t.json())["billing statuses"];if(!Array.isArray(a))throw new Error("Expected billing statuses to be an array.");const s=a.find((e=>e.id===r));if(!s)throw new Error("Billing status not found.");n(s.name)}catch(o){d(o.message)}else d("No authentication token found.")})()}),[r]);return t?(0,g.jsx)("div",{className:"fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50",onClick:()=>a(!1),children:(0,g.jsxs)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full",onClick:e=>e.stopPropagation(),children:[(0,g.jsxs)("div",{className:"flex justify-between items-center mb-4 bg-gray-100 p-4",children:[(0,g.jsx)("h3",{className:"text-base text-left font-medium text-gray-800",children:"Update Billing Status"}),(0,g.jsx)("button",{className:"text-2xl text-gray-500 hover:text-gray-800",onClick:()=>a(!1),children:"\xd7"})]}),(0,g.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=m;if(!t)return void d("User is not logged in.");const s=localStorage.getItem("token");if(s)try{const e=await fetch(`${x}/billing_statuses/${r}`,{method:"PUT",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"},body:JSON.stringify({name:l.trim(),updated_by:t})});if(!e.ok)throw new Error("Failed to update Billing status: "+e.statusText);const n=await e.json();(0,i.GW)({icon:"success",title:"Success!",text:(null===n||void 0===n?void 0:n.message)||"Billing status updated successfully."}),setTimeout((()=>{a(!1),u("")}),1e3)}catch(o){(0,i.GW)("error")}else d("Authentication token is missing.")},className:"p-6",children:[(0,g.jsxs)("div",{className:"mb-4",children:[(0,g.jsx)("label",{htmlFor:"name",className:"block mb-2 text-left text-sm text-gray-600",children:"Billing Status Name"}),(0,g.jsx)("input",{type:"text",id:"name",value:l,onChange:e=>n(e.target.value),className:"border rounded w-full p-2",required:!0})]}),(0,g.jsx)("button",{type:"submit",className:"bg-primary hover:bg-secondary text-white rounded-md px-4 py-2",children:"Update Billing Status"})]})]})}):null};var h=a(5094),f=a(17974),b=a(58598);const y="Billing Status",v=()=>{const[e,t]=(0,s.useState)({}),[a,x]=(0,s.useState)({}),[v,j]=(0,s.useState)(""),[w,N]=(0,s.useState)(""),[_,k]=(0,s.useState)(!1),[S,C]=(0,s.useState)(!1),[A,$]=(0,s.useState)(null),[E,F]=(0,s.useState)(null),[B,O]=(0,s.useState)(null),[D,T]=((0,m.Zp)(),(0,s.useState)(!1)),[I,P]=(0,s.useState)("created_at"),[R,M]=(0,s.useState)("desc"),[U,V]=(0,s.useState)("10"),[z,W]=(0,s.useState)(1),{data:q,isFetching:G,error:H}=(0,u.lDm)({sort_by:I,order:R,page:z,per_page:U,query:w}),[L,{data:Q,error:J}]=(0,u.u_D)(),[Y]=(0,u.HI6)(),Z=e=>{let t=Object.entries(e).reduce(((e,t)=>{let[a,s]=t;if("string"===typeof s)return e+`&${a}=${s}`;if(Array.isArray(s)){return e+`&${a}=${s.map((e=>e.value)).join(",")}`}return e}),"");N(t)},K=e=>{(0,o.$3)(e,["id","team","department","updated_at","updated_by","updater","created_at","creator","created_by","updated_by"]);O(null),k(!0)},X=e=>{O(null),$(e),k(!0)},ee=e=>{(0,i.YU)({onConfirm:()=>{Y(e),O(null)}})};let te=1;const{rolePermissions:ae}=(0,f.h)(),[se,re]=(0,s.useState)((()=>[{id:te++,name:"Action",width:"180px",className:"bg-red-300",cell:e=>(0,g.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,g.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>O(e),children:(0,g.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,g.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,g.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,g.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e),children:(0,g.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,g.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,g.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})},{id:te++,name:"S.No",selector:(e,t)=>(z-1)*U+t+1,width:"80px",omit:!1},{id:te++,name:"Billing Status Name",db_field:"name",selector:e=>e.name||"",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created by",selector:e=>{var t,a;return`${(null===(t=e.creator)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.creator)||void 0===a?void 0:a.lname)||""}`},db_field:"created_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Date",selector:e=>(0,b.hb)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Created Time",selector:e=>(0,b.DF)(e.created_at),db_field:"created_at",omit:!1,sortable:!0,filterable:!1},{id:te++,name:"Updated by",selector:e=>{var t,a;return`${(null===(t=e.updater)||void 0===t?void 0:t.fname)||""} ${(null===(a=e.updater)||void 0===a?void 0:a.lname)||""}`},db_field:"updated_by",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Date",selector:e=>(0,b.hb)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!0},{id:te++,name:"Updated Time",selector:e=>(0,b.DF)(e.updated_at),db_field:"updated_at",omit:!1,sortable:!0,filterable:!1}]));(0,s.useEffect)((()=>{re((e=>[...e.map((e=>"Action"===e.name?{...e,cell:e=>(0,g.jsxs)("div",{className:"flex gap-1 mx-2 !min-w-[200px] pl-3",children:[(0,g.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>O(e),children:(0,g.jsx)("span",{className:"material-symbols-outlined text-lg",children:"visibility"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,g.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>X(e.id),children:(0,g.jsx)("span",{className:"material-symbols-outlined text-lg",children:"stylus_note"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,g.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>K(e),children:(0,g.jsx)("span",{className:"material-symbols-outlined text-lg",children:"content_copy"})}),(null===ae||void 0===ae?void 0:ae.hasManagerRole)&&(0,g.jsx)("button",{className:"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200",onClick:()=>ee(e.id),children:(0,g.jsx)("span",{className:"material-symbols-outlined text-sm",children:"delete"})})]})}:e))]))}),[ae]);const le=(0,n.wA)(),ie=(0,s.useCallback)((async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"group",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"select",l=e.db_field||"title";try{j(l),C(!0);var i=[];const n=await L({type:a.trim(),column:l.trim(),text:s.trim()});if(n.data&&(i=n.data),i.length){if("searchable"===r)return t((e=>({...e,[l]:i}))),i;const a=i.map((t=>{if(e.selector){let a=e.selector(t);return a?(t.total&&t.total>1&&(a+=` (${t.total})`),{label:a,value:t[l]}):null}})).filter(Boolean);return t((t=>({...t,[e.id]:(0,o.eb)(a)}))),a}}catch(E){F(E.message)}finally{C(!1)}}),[]);return(0,g.jsx)("section",{className:"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]",children:(0,g.jsxs)("div",{className:"mx-auto pb-6 ",children:[(0,g.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4",children:[(0,g.jsx)("div",{className:"w-4/12 md:w-10/12 text-start",children:(0,g.jsx)("h2",{className:"text-2xl font-bold ",children:y})}),(0,g.jsxs)("div",{className:"w-8/12 flex items-end justify-end gap-1",children:[(0,g.jsx)(i.DF,{columns:se,setColumns:re}),!G&&q&&parseInt(q.total)>0&&(0,g.jsx)(g.Fragment,{children:(0,g.jsxs)("button",{className:"w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:async()=>{try{const t=await le(u._l6.endpoints.getBillingStatusData.initiate({sort_by:I,order:R,page:z,per_page:(null===q||void 0===q?void 0:q.total)||10,query:w})).unwrap();if(null===t||void 0===t||!t.total||t.total<1)return!1;var e=1;let a=t.data.map((t=>{if(se.length){let a={};return se.forEach((s=>{!s.omit&&s.selector&&(a[s.name]="S.No"===s.name?e++:s.selector(t)||"")})),a}}));const s=c.Wp.json_to_sheet(a),r=c.Wp.book_new();c.Wp.book_append_sheet(r,s,"Sheet1");const l=c.M9(r,{bookType:"xlsx",type:"array"}),i=new Blob([l],{type:"application/octet-stream"});(0,d.saveAs)(i,`${y.replace(/ /g,"_")}_${a.length}.xlsx`)}catch(E){console.error("Error exporting to Excel:",E)}},children:[G&&(0,g.jsx)(g.Fragment,{children:(0,g.jsx)("span",{className:"material-symbols-outlined animate-spin text-sm me-2",children:"progress_activity"})}),!G&&(0,g.jsx)("span",{className:"material-symbols-outlined text-sm me-2",children:"file_export"}),"Export to Excel (",q.total,")"]})}),ae.hasManagerRole&&(0,g.jsx)("button",{className:" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:()=>T(!0),children:"Add New"})]})]}),(0,g.jsx)(i.$6,{columns:se,selectedFilterOptions:a,setSelectedFilterOptions:x,fetchDataOptionsForFilterBy:ie,filterOptions:e,filterOptionLoading:S,showFilterOption:v,resetPage:()=>{if(Object.keys(a).length){let e={};Object.keys(a).map((t=>{"string"===typeof a[t]?e[t]="":e[t]=[]})),x({...e}),Z({...e})}W(1)},setCurrentPage:W,buildQueryParams:Z}),H&&(0,g.jsx)("div",{className:"text-red-500",children:E}),G&&(0,g.jsx)(l.A,{}),(0,g.jsx)("div",{className:"border border-gray-200 p-0 pb-1 rounded-lg my-5 ",children:(0,g.jsx)(r.Ay,{columns:se,data:(null===q||void 0===q?void 0:q.data)||[],className:"p-0 scrollbar-horizontal-10",fixedHeader:!0,highlightOnHover:!0,responsive:!0,pagination:!0,paginationServer:!0,paginationPerPage:U,paginationTotalRows:(null===q||void 0===q?void 0:q.total)||0,onChangePage:e=>{e!==z&&W(e)},onChangeRowsPerPage:e=>{e!==U&&(V(e),W(1))},paginationComponentOptions:{selectAllRowsItem:!0,selectAllRowsItemText:"ALL"},sortServer:!0,onSort:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"desc";Object.keys(e).length&&(P(e.db_field||e.name||"created_at"),M(t||"desc"))}})}),D&&(0,g.jsx)(h.A,{isVisible:D,setVisible:T}),_&&(0,g.jsx)(p,{isVisible:_,setVisible:k,dataItemsId:A}),B&&(0,g.jsx)(i.Qg,{item:B,setViewData:O,columns:se,handleEdit:X,handleDelete:ee})]})})},j=()=>(0,g.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-xl",children:(0,g.jsx)(v,{})})}}]);
//# sourceMappingURL=352.3873eb01.chunk.js.map