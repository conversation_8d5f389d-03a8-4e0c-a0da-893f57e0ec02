<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PasswordManagerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        // Conditional password exposure for security
        $includePassword = $request->query('include_password', 'true') === 'true' ||
                          $request->is('*/password') ||
                          $request->isMethod('POST') ||
                          $request->isMethod('PUT');

        $data = [
            'id' => $this->id,
            'title' => $this->password_title,
            'username' => $this->username,
            'level' => $this->getPasswordStrength(),
            'department_id' => $this->department_id,
            'team_id' => $this->team_id,
            'user_id' => $this->user_id,
            'password_title' => $this->password_title,
            'password_strength' => $this->getPasswordStrength(),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];

        // Include decrypted password if applicable
        if ($includePassword) {
            $decryptedPassword = $this->getDecryptedPassword();
            $data['password'] = $decryptedPassword ?? '';
            if ($decryptedPassword === null) {
                $data['password_error'] = 'Unable to decrypt password';
            }
        }

        // Conditional relationship loading
        $data = array_merge($data, [
            'department' => $this->whenLoaded('department', function () {
                return [
                    'id' => $this->department->id,
                    'name' => $this->department->name,
                ];
            }),
            'team' => $this->whenLoaded('team', function () {
                return [
                    'id' => $this->team->id,
                    'name' => $this->team->name,
                ];
            }),
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'eid' => $this->user->eid,
                    'fname' => $this->user->fname,
                    'lname' => $this->user->lname,
                    'full_name' => trim(($this->user->fname ?? '') . ' ' . ($this->user->lname ?? '')),
                    'email' => $this->user->email,
                    'photo' => $this->user->photo ? url('storage/' . $this->user->photo) : null,
                ];
            }),
        ]);

        return $data;
    }
}