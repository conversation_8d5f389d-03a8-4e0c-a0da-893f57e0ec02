import React, { useEffect, useState } from "react";
import Loading from "../common/Loading";

const normalizeShift = (item, idx) => {
  const name = item?.name || item?.shift || item?.title || ["Morning", "Evening", "Night"][idx % 3];
  const stats = item?.stats || item?.counts || item || {};
  return {
    name,
    designer:
      stats?.designer ?? stats?.designers ?? stats?.designer_count ?? stats?.total_designer ?? 0,
    developer:
      stats?.developer ?? stats?.developers ?? stats?.developer_count ?? stats?.total_developer ?? 0,
    qa: stats?.qa ?? stats?.qa_count ?? stats?.quality_assurance ?? stats?.total_qa ?? 0,
  };
};

const SmallStat = ({ label, value }) => (
  <div className="rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 px-4 py-3 flex items-center justify-between">
    <div className="text-sm font-medium text-gray-700 dark:text-gray-200">{label}</div>
    <div className="flex items-center gap-2 text-gray-900 dark:text-gray-100">
      <span>👤</span>
      <span className="font-semibold">{String(value).padStart(2, "0")}</span>
    </div>
  </div>
);

const ShiftCard = ({ shift }) => (
  <div className="rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4">
    <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">{shift.name} Shift</h4>
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
      <SmallStat label="Total Designer" value={shift.designer} />
      <SmallStat label="Total Developer" value={shift.developer} />
      <SmallStat label="Total QA" value={shift.qa} />
    </div>
  </div>
);

const ShiftSummarySection = () => {
  const [shifts, setShifts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [err, setErr] = useState(null);

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (!token) {
      setErr("No auth token found");
      setLoading(false);
      return;
    }
    const load = async () => {
      setLoading(true);
      try {
        const mockShifts = [
          {
            name: "Evening",
            stats: {
              designer: 20,
              developer: 25,
              qa: 6
            }
          },
          {
            name: "Morning",
            stats: {
              designer: 20,
              developer: 25,
              qa: 6
            }
          },
          {
            name: "Night",
            stats: {
              designer: 20,
              developer: 25,
              qa: 6
            }
          }
        ];

        const normalized = mockShifts.map((item, idx) => normalizeShift(item, idx));
        const order = ["evening", "morning", "night"];
        normalized.sort(
          (a, b) => order.indexOf((a.name || "").toLowerCase()) - order.indexOf((b.name || "").toLowerCase())
        );
        setShifts(normalized);
      } catch (error) {
        console.error('Error:', error);
        setErr('Unable to load shift data');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, []);

  if (loading) return <Loading />;
  if (err) return <div className="text-red-500">{err}</div>;
  if (!shifts.length) return null;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
      {shifts.map((s, i) => (
        <ShiftCard key={`${s.name}-${i}`} shift={s} />
      ))}
    </div>
  );
};

export default ShiftSummarySection;