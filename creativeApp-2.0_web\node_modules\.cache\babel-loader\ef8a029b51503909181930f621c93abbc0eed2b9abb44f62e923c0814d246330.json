{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{API_URL}from\"../common/fetchData/apiConfig\";import Loading from\"../common/Loading\";// Component to display a single team card\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TeamCard=_ref=>{var _team$name,_team$name$;let{team}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\",children:team.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 mt-1\",children:[team.isTopClient&&/*#__PURE__*/_jsx(\"span\",{title:\"Top Client\",className:\"text-amber-500 text-sm\",children:\"\\uD83D\\uDD06 Priority Client\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"|\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:team.shift})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center gap-2\",children:team.logo?/*#__PURE__*/_jsx(\"img\",{src:team.logo,alt:`${team.name} logo`,className:\"w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600\"}):/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-lg font-medium text-gray-500\",children:((_team$name=team.name)===null||_team$name===void 0?void 0:(_team$name$=_team$name[0])===null||_team$name$===void 0?void 0:_team$name$.toUpperCase())||\"T\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 space-y-2\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-gray-400\",children:\"person\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Lead:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-gray-900 dark:text-gray-100\",children:team.teamLead})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-gray-400\",children:\"payments\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Status:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-gray-900 dark:text-gray-100\",children:team.billingStatus})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-600 dark:text-gray-300\",children:\"Total Members\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDC65\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:team.totalMembers})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-600 dark:text-gray-300\",children:\"Billable Hours\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u23F1\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:team.billableHours})]})]})]}),team.billingRate>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 text-xs text-right text-gray-500\",children:[\"Rate: $\",team.billingRate,\"/hr\"]})]});};// Function to normalize team data\nconst normalizeTeam=(item,idx)=>{var _ref2,_ref3,_item$id,_item$team_lead,_ref4,_ref5,_item$total_members,_ref6,_ref7,_item$billable_hours,_ref8,_ref9,_item$is_top_client;const name=(item===null||item===void 0?void 0:item.name)||(item===null||item===void 0?void 0:item.team_name)||(item===null||item===void 0?void 0:item.client_name)||`Team ${idx+1}`;return{id:(_ref2=(_ref3=(_item$id=item===null||item===void 0?void 0:item.id)!==null&&_item$id!==void 0?_item$id:item===null||item===void 0?void 0:item.team_id)!==null&&_ref3!==void 0?_ref3:item===null||item===void 0?void 0:item.client_id)!==null&&_ref2!==void 0?_ref2:`${name}-${idx}`,name,teamLead:(item===null||item===void 0?void 0:(_item$team_lead=item.team_lead)===null||_item$team_lead===void 0?void 0:_item$team_lead.name)||(item===null||item===void 0?void 0:item.team_lead)||(item===null||item===void 0?void 0:item.lead_name)||\"—\",totalMembers:String((_ref4=(_ref5=(_item$total_members=item===null||item===void 0?void 0:item.total_members)!==null&&_item$total_members!==void 0?_item$total_members:item===null||item===void 0?void 0:item.members_count)!==null&&_ref5!==void 0?_ref5:item===null||item===void 0?void 0:item.member_count)!==null&&_ref4!==void 0?_ref4:0).padStart(2,'0'),billableHours:`${(_ref6=(_ref7=(_item$billable_hours=item===null||item===void 0?void 0:item.billable_hours)!==null&&_item$billable_hours!==void 0?_item$billable_hours:item===null||item===void 0?void 0:item.hours)!==null&&_ref7!==void 0?_ref7:item===null||item===void 0?void 0:item.hour)!==null&&_ref6!==void 0?_ref6:0}hr`,logo:(item===null||item===void 0?void 0:item.logo_url)||(item===null||item===void 0?void 0:item.icon)||(item===null||item===void 0?void 0:item.team_logo)||(item===null||item===void 0?void 0:item.client_logo)||`/assets/client-logos/${name.toLowerCase().replace(/\\s+/g,'-')}.png`,isTopClient:Boolean((_ref8=(_ref9=(_item$is_top_client=item===null||item===void 0?void 0:item.is_top_client)!==null&&_item$is_top_client!==void 0?_item$is_top_client:item===null||item===void 0?void 0:item.top_client)!==null&&_ref9!==void 0?_ref9:item===null||item===void 0?void 0:item.priority_client)!==null&&_ref8!==void 0?_ref8:false),billingStatus:(item===null||item===void 0?void 0:item.billing_status)||\"Not Set\",shift:(item===null||item===void 0?void 0:item.shift)||\"Not Assigned\",billingRate:(item===null||item===void 0?void 0:item.billing_rate)||0};};// Main component\nfunction ClientTeamsSection(){const[teams,setTeams]=useState([]);const[loading,setLoading]=useState(true);const[err,setErr]=useState(null);useEffect(()=>{const token=localStorage.getItem(\"token\");if(!token){setErr(\"No auth token found\");setLoading(false);return;}const load=async()=>{setLoading(true);try{const mockTeams=[{name:\"AccuWeather\",team_lead:\"Kamal Hossain\",total_members:10,billable_hours:80,logo:\"/assets/client-logos/accuweather.png\",is_top_client:true},{name:\"Bloomberg\",team_lead:\"Kamal Hossain\",total_members:8,billable_hours:64,logo:\"/assets/client-logos/bloomberg.png\",is_top_client:true},{name:\"Boats Group\",team_lead:\"Aminul Islam\",total_members:13,billable_hours:104,logo:\"/assets/client-logos/boats-group.png\",is_top_client:true},{name:\"Clipcentric\",team_lead:\"Aminul Islam\",total_members:15,billable_hours:120,logo:\"/assets/client-logos/clipcentric.png\"},{name:\"MultiView\",team_lead:\"Hasan Ahmed\",total_members:5,billable_hours:40,logo:\"/assets/client-logos/multiview.png\"},{name:\"Bigtincan\",team_lead:\"Nafiul Islam\",total_members:5,billable_hours:40,logo:\"/assets/client-logos/bigtincan.png\"}];let finalTeams=mockTeams;try{const response=await fetch(`${API_URL}/teams`,{headers:{'Authorization':`Bearer ${token}`,'Content-Type':'application/json','Accept':'application/json'}});if(response.ok){var _data$data;const data=await response.json();if((data===null||data===void 0?void 0:(_data$data=data.data)===null||_data$data===void 0?void 0:_data$data.length)>0||Array.isArray(data)&&data.length>0){finalTeams=(data===null||data===void 0?void 0:data.data)||data;}}}catch(error){console.warn('Failed to fetch from API, using mock data:',error);}setTeams(finalTeams.map((item,idx)=>normalizeTeam(item,idx)));}catch(error){console.error(\"Error fetching teams:\",error);setErr(\"Unable to load teams. Please try again later.\");}finally{setLoading(false);}};load();},[]);if(loading)return/*#__PURE__*/_jsx(Loading,{});if(err)return/*#__PURE__*/_jsx(\"div\",{className:\"text-red-500\",children:err});if(!teams.length)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4\",children:teams.map(team=>/*#__PURE__*/_jsx(TeamCard,{team:team},team.id))});}// Export the component\nexport default ClientTeamsSection;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "API_URL", "Loading", "jsx", "_jsx", "jsxs", "_jsxs", "TeamCard", "_ref", "_team$name", "_team$name$", "team", "className", "children", "name", "isTopClient", "title", "shift", "logo", "src", "alt", "toUpperCase", "teamLead", "billingStatus", "totalMembers", "billableHours", "billingRate", "normalizeTeam", "item", "idx", "_ref2", "_ref3", "_item$id", "_item$team_lead", "_ref4", "_ref5", "_item$total_members", "_ref6", "_ref7", "_item$billable_hours", "_ref8", "_ref9", "_item$is_top_client", "team_name", "client_name", "id", "team_id", "client_id", "team_lead", "lead_name", "String", "total_members", "members_count", "member_count", "padStart", "billable_hours", "hours", "hour", "logo_url", "icon", "team_logo", "client_logo", "toLowerCase", "replace", "Boolean", "is_top_client", "top_client", "priority_client", "billing_status", "billing_rate", "ClientTeamsSection", "teams", "setTeams", "loading", "setLoading", "err", "setErr", "token", "localStorage", "getItem", "load", "mockTeams", "finalTeams", "response", "fetch", "headers", "ok", "_data$data", "data", "json", "length", "Array", "isArray", "error", "console", "warn", "map"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/ClientTeamsSection.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { API_URL } from \"../common/fetchData/apiConfig\";\r\nimport Loading from \"../common/Loading\";\r\n\r\n// Component to display a single team card\r\nconst TeamCard = ({ team }) => (\r\n  <div className=\"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow p-4\">\r\n    <div className=\"flex items-center justify-between\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\">{team.name}</h3>\r\n        <div className=\"flex items-center gap-2 mt-1\">\r\n          {team.isTopClient && <span title=\"Top Client\" className=\"text-amber-500 text-sm\">🔆 Priority Client</span>}\r\n          <span className=\"text-xs text-gray-500\">|</span>\r\n          <span className=\"text-sm text-gray-600\">{team.shift}</span>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex items-center gap-2\">\r\n        {team.logo ? (\r\n          <img\r\n            src={team.logo}\r\n            alt={`${team.name} logo`}\r\n            className=\"w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600\"\r\n          />\r\n        ) : (\r\n          <div className=\"w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-lg font-medium text-gray-500\">\r\n            {team.name?.[0]?.toUpperCase() || \"T\"}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n\r\n    <div className=\"mt-4 space-y-2\">\r\n      <p className=\"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\">\r\n        <span className=\"material-symbols-outlined text-gray-400\">person</span>\r\n        <span>Lead:</span>\r\n        <span className=\"font-medium text-gray-900 dark:text-gray-100\">{team.teamLead}</span>\r\n      </p>\r\n      \r\n      <p className=\"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\">\r\n        <span className=\"material-symbols-outlined text-gray-400\">payments</span>\r\n        <span>Status:</span>\r\n        <span className=\"font-medium text-gray-900 dark:text-gray-100\">{team.billingStatus}</span>\r\n      </p>\r\n    </div>\r\n\r\n    <div className=\"mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-3\">\r\n      <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\">\r\n        <p className=\"text-xs text-gray-600 dark:text-gray-300\">Total Members</p>\r\n        <div className=\"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n          <span>👥</span>\r\n          <span className=\"font-semibold\">{team.totalMembers}</span>\r\n        </div>\r\n      </div>\r\n      <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\">\r\n        <p className=\"text-xs text-gray-600 dark:text-gray-300\">Billable Hours</p>\r\n        <div className=\"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n          <span>⏱️</span>\r\n          <span className=\"font-semibold\">{team.billableHours}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    {team.billingRate > 0 && (\r\n      <div className=\"mt-3 text-xs text-right text-gray-500\">\r\n        Rate: ${team.billingRate}/hr\r\n      </div>\r\n    )}\r\n  </div>\r\n);\r\n\r\n// Function to normalize team data\r\nconst normalizeTeam = (item, idx) => {\r\n  const name = item?.name || item?.team_name || item?.client_name || `Team ${idx + 1}`;\r\n  return {\r\n    id: item?.id ?? item?.team_id ?? item?.client_id ?? `${name}-${idx}`,\r\n    name,\r\n    teamLead: item?.team_lead?.name || item?.team_lead || item?.lead_name || \"—\",\r\n    totalMembers: String(item?.total_members ?? item?.members_count ?? item?.member_count ?? 0).padStart(2, '0'),\r\n    billableHours: `${item?.billable_hours ?? item?.hours ?? item?.hour ?? 0}hr`,\r\n    logo: item?.logo_url || item?.icon || item?.team_logo || item?.client_logo || `/assets/client-logos/${name.toLowerCase().replace(/\\s+/g, '-')}.png`,\r\n    isTopClient: Boolean(item?.is_top_client ?? item?.top_client ?? item?.priority_client ?? false),\r\n    billingStatus: item?.billing_status || \"Not Set\",\r\n    shift: item?.shift || \"Not Assigned\",\r\n    billingRate: item?.billing_rate || 0,\r\n  };\r\n};\r\n\r\n// Main component\r\nfunction ClientTeamsSection() {\r\n  const [teams, setTeams] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [err, setErr] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (!token) {\r\n      setErr(\"No auth token found\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    const load = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const mockTeams = [\r\n          {\r\n            name: \"AccuWeather\",\r\n            team_lead: \"Kamal Hossain\",\r\n            total_members: 10,\r\n            billable_hours: 80,\r\n            logo: \"/assets/client-logos/accuweather.png\",\r\n            is_top_client: true\r\n          },\r\n          {\r\n            name: \"Bloomberg\",\r\n            team_lead: \"Kamal Hossain\",\r\n            total_members: 8,\r\n            billable_hours: 64,\r\n            logo: \"/assets/client-logos/bloomberg.png\",\r\n            is_top_client: true\r\n          },\r\n          {\r\n            name: \"Boats Group\",\r\n            team_lead: \"Aminul Islam\",\r\n            total_members: 13,\r\n            billable_hours: 104,\r\n            logo: \"/assets/client-logos/boats-group.png\",\r\n            is_top_client: true\r\n          },\r\n          {\r\n            name: \"Clipcentric\",\r\n            team_lead: \"Aminul Islam\",\r\n            total_members: 15,\r\n            billable_hours: 120,\r\n            logo: \"/assets/client-logos/clipcentric.png\"\r\n          },\r\n          {\r\n            name: \"MultiView\",\r\n            team_lead: \"Hasan Ahmed\",\r\n            total_members: 5,\r\n            billable_hours: 40,\r\n            logo: \"/assets/client-logos/multiview.png\"\r\n          },\r\n          {\r\n            name: \"Bigtincan\",\r\n            team_lead: \"Nafiul Islam\",\r\n            total_members: 5,\r\n            billable_hours: 40,\r\n            logo: \"/assets/client-logos/bigtincan.png\"\r\n          }\r\n        ];\r\n\r\n        let finalTeams = mockTeams;\r\n\r\n        try {\r\n          const response = await fetch(`${API_URL}/teams`, {\r\n            headers: { \r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json',\r\n              'Accept': 'application/json'\r\n            },\r\n          });\r\n\r\n          if (response.ok) {\r\n            const data = await response.json();\r\n            if (data?.data?.length > 0 || (Array.isArray(data) && data.length > 0)) {\r\n              finalTeams = data?.data || data;\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.warn('Failed to fetch from API, using mock data:', error);\r\n        }\r\n\r\n        setTeams(finalTeams.map((item, idx) => normalizeTeam(item, idx)));\r\n      } catch (error) {\r\n        console.error(\"Error fetching teams:\", error);\r\n        setErr(\"Unable to load teams. Please try again later.\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    load();\r\n  }, []);\r\n\r\n  if (loading) return <Loading />;\r\n  if (err) return <div className=\"text-red-500\">{err}</div>;\r\n  if (!teams.length) return null;\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4\">\r\n      {teams.map((team) => (\r\n        <TeamCard key={team.id} team={team} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\n// Export the component\r\nexport default ClientTeamsSection;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,OAAO,KAAQ,+BAA+B,CACvD,MAAO,CAAAC,OAAO,KAAM,mBAAmB,CAEvC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,QAAQ,CAAGC,IAAA,OAAAC,UAAA,CAAAC,WAAA,IAAC,CAAEC,IAAK,CAAC,CAAAH,IAAA,oBACxBF,KAAA,QAAKM,SAAS,CAAC,mIAAmI,CAAAC,QAAA,eAChJP,KAAA,QAAKM,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDP,KAAA,QAAAO,QAAA,eACET,IAAA,OAAIQ,SAAS,CAAC,iEAAiE,CAAAC,QAAA,CAAEF,IAAI,CAACG,IAAI,CAAK,CAAC,cAChGR,KAAA,QAAKM,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAC1CF,IAAI,CAACI,WAAW,eAAIX,IAAA,SAAMY,KAAK,CAAC,YAAY,CAACJ,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,8BAAkB,CAAM,CAAC,cAC1GT,IAAA,SAAMQ,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,cAChDT,IAAA,SAAMQ,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEF,IAAI,CAACM,KAAK,CAAO,CAAC,EACxD,CAAC,EACH,CAAC,cACNb,IAAA,QAAKQ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCF,IAAI,CAACO,IAAI,cACRd,IAAA,QACEe,GAAG,CAAER,IAAI,CAACO,IAAK,CACfE,GAAG,CAAE,GAAGT,IAAI,CAACG,IAAI,OAAQ,CACzBF,SAAS,CAAC,6EAA6E,CACxF,CAAC,cAEFR,IAAA,QAAKQ,SAAS,CAAC,wHAAwH,CAAAC,QAAA,CACpI,EAAAJ,UAAA,CAAAE,IAAI,CAACG,IAAI,UAAAL,UAAA,kBAAAC,WAAA,CAATD,UAAA,CAAY,CAAC,CAAC,UAAAC,WAAA,iBAAdA,WAAA,CAAgBW,WAAW,CAAC,CAAC,GAAI,GAAG,CAClC,CACN,CACE,CAAC,EACH,CAAC,cAENf,KAAA,QAAKM,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BP,KAAA,MAAGM,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC7ET,IAAA,SAAMQ,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cACvET,IAAA,SAAAS,QAAA,CAAM,OAAK,CAAM,CAAC,cAClBT,IAAA,SAAMQ,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAEF,IAAI,CAACW,QAAQ,CAAO,CAAC,EACpF,CAAC,cAEJhB,KAAA,MAAGM,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC7ET,IAAA,SAAMQ,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cACzET,IAAA,SAAAS,QAAA,CAAM,SAAO,CAAM,CAAC,cACpBT,IAAA,SAAMQ,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAEF,IAAI,CAACY,aAAa,CAAO,CAAC,EACzF,CAAC,EACD,CAAC,cAENjB,KAAA,QAAKM,SAAS,CAAC,gFAAgF,CAAAC,QAAA,eAC7FP,KAAA,QAAKM,SAAS,CAAC,2FAA2F,CAAAC,QAAA,eACxGT,IAAA,MAAGQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,eAAa,CAAG,CAAC,cACzEP,KAAA,QAAKM,SAAS,CAAC,+DAA+D,CAAAC,QAAA,eAC5ET,IAAA,SAAAS,QAAA,CAAM,cAAE,CAAM,CAAC,cACfT,IAAA,SAAMQ,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEF,IAAI,CAACa,YAAY,CAAO,CAAC,EACvD,CAAC,EACH,CAAC,cACNlB,KAAA,QAAKM,SAAS,CAAC,2FAA2F,CAAAC,QAAA,eACxGT,IAAA,MAAGQ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gBAAc,CAAG,CAAC,cAC1EP,KAAA,QAAKM,SAAS,CAAC,+DAA+D,CAAAC,QAAA,eAC5ET,IAAA,SAAAS,QAAA,CAAM,cAAE,CAAM,CAAC,cACfT,IAAA,SAAMQ,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEF,IAAI,CAACc,aAAa,CAAO,CAAC,EACxD,CAAC,EACH,CAAC,EACH,CAAC,CAELd,IAAI,CAACe,WAAW,CAAG,CAAC,eACnBpB,KAAA,QAAKM,SAAS,CAAC,uCAAuC,CAAAC,QAAA,EAAC,SAC9C,CAACF,IAAI,CAACe,WAAW,CAAC,KAC3B,EAAK,CACN,EACE,CAAC,EACP,CAED;AACA,KAAM,CAAAC,aAAa,CAAGA,CAACC,IAAI,CAAEC,GAAG,GAAK,KAAAC,KAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,mBAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,oBAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,mBAAA,CACnC,KAAM,CAAA5B,IAAI,CAAG,CAAAc,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEd,IAAI,IAAIc,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEe,SAAS,IAAIf,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEgB,WAAW,GAAI,QAAQf,GAAG,CAAG,CAAC,EAAE,CACpF,MAAO,CACLgB,EAAE,EAAAf,KAAA,EAAAC,KAAA,EAAAC,QAAA,CAAEJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiB,EAAE,UAAAb,QAAA,UAAAA,QAAA,CAAIJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEkB,OAAO,UAAAf,KAAA,UAAAA,KAAA,CAAIH,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEmB,SAAS,UAAAjB,KAAA,UAAAA,KAAA,CAAI,GAAGhB,IAAI,IAAIe,GAAG,EAAE,CACpEf,IAAI,CACJQ,QAAQ,CAAE,CAAAM,IAAI,SAAJA,IAAI,kBAAAK,eAAA,CAAJL,IAAI,CAAEoB,SAAS,UAAAf,eAAA,iBAAfA,eAAA,CAAiBnB,IAAI,IAAIc,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoB,SAAS,IAAIpB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEqB,SAAS,GAAI,GAAG,CAC5EzB,YAAY,CAAE0B,MAAM,EAAAhB,KAAA,EAAAC,KAAA,EAAAC,mBAAA,CAACR,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEuB,aAAa,UAAAf,mBAAA,UAAAA,mBAAA,CAAIR,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEwB,aAAa,UAAAjB,KAAA,UAAAA,KAAA,CAAIP,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEyB,YAAY,UAAAnB,KAAA,UAAAA,KAAA,CAAI,CAAC,CAAC,CAACoB,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC5G7B,aAAa,CAAE,IAAAY,KAAA,EAAAC,KAAA,EAAAC,oBAAA,CAAGX,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2B,cAAc,UAAAhB,oBAAA,UAAAA,oBAAA,CAAIX,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE4B,KAAK,UAAAlB,KAAA,UAAAA,KAAA,CAAIV,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6B,IAAI,UAAApB,KAAA,UAAAA,KAAA,CAAI,CAAC,IAAI,CAC5EnB,IAAI,CAAE,CAAAU,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE8B,QAAQ,IAAI9B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE+B,IAAI,IAAI/B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEgC,SAAS,IAAIhC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiC,WAAW,GAAI,wBAAwB/C,IAAI,CAACgD,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,CAAE,GAAG,CAAC,MAAM,CACnJhD,WAAW,CAAEiD,OAAO,EAAAxB,KAAA,EAAAC,KAAA,EAAAC,mBAAA,CAACd,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEqC,aAAa,UAAAvB,mBAAA,UAAAA,mBAAA,CAAId,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEsC,UAAU,UAAAzB,KAAA,UAAAA,KAAA,CAAIb,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEuC,eAAe,UAAA3B,KAAA,UAAAA,KAAA,CAAI,KAAK,CAAC,CAC/FjB,aAAa,CAAE,CAAAK,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEwC,cAAc,GAAI,SAAS,CAChDnD,KAAK,CAAE,CAAAW,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEX,KAAK,GAAI,cAAc,CACpCS,WAAW,CAAE,CAAAE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEyC,YAAY,GAAI,CACrC,CAAC,CACH,CAAC,CAED;AACA,QAAS,CAAAC,kBAAkBA,CAAA,CAAG,CAC5B,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGxE,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACyE,OAAO,CAAEC,UAAU,CAAC,CAAG1E,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2E,GAAG,CAAEC,MAAM,CAAC,CAAG5E,QAAQ,CAAC,IAAI,CAAC,CAEpCD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8E,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAI,CAACF,KAAK,CAAE,CACVD,MAAM,CAAC,qBAAqB,CAAC,CAC7BF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA,KAAM,CAAAM,IAAI,CAAG,KAAAA,CAAA,GAAY,CACvBN,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAO,SAAS,CAAG,CAChB,CACEnE,IAAI,CAAE,aAAa,CACnBkC,SAAS,CAAE,eAAe,CAC1BG,aAAa,CAAE,EAAE,CACjBI,cAAc,CAAE,EAAE,CAClBrC,IAAI,CAAE,sCAAsC,CAC5C+C,aAAa,CAAE,IACjB,CAAC,CACD,CACEnD,IAAI,CAAE,WAAW,CACjBkC,SAAS,CAAE,eAAe,CAC1BG,aAAa,CAAE,CAAC,CAChBI,cAAc,CAAE,EAAE,CAClBrC,IAAI,CAAE,oCAAoC,CAC1C+C,aAAa,CAAE,IACjB,CAAC,CACD,CACEnD,IAAI,CAAE,aAAa,CACnBkC,SAAS,CAAE,cAAc,CACzBG,aAAa,CAAE,EAAE,CACjBI,cAAc,CAAE,GAAG,CACnBrC,IAAI,CAAE,sCAAsC,CAC5C+C,aAAa,CAAE,IACjB,CAAC,CACD,CACEnD,IAAI,CAAE,aAAa,CACnBkC,SAAS,CAAE,cAAc,CACzBG,aAAa,CAAE,EAAE,CACjBI,cAAc,CAAE,GAAG,CACnBrC,IAAI,CAAE,sCACR,CAAC,CACD,CACEJ,IAAI,CAAE,WAAW,CACjBkC,SAAS,CAAE,aAAa,CACxBG,aAAa,CAAE,CAAC,CAChBI,cAAc,CAAE,EAAE,CAClBrC,IAAI,CAAE,oCACR,CAAC,CACD,CACEJ,IAAI,CAAE,WAAW,CACjBkC,SAAS,CAAE,cAAc,CACzBG,aAAa,CAAE,CAAC,CAChBI,cAAc,CAAE,EAAE,CAClBrC,IAAI,CAAE,oCACR,CAAC,CACF,CAED,GAAI,CAAAgE,UAAU,CAAGD,SAAS,CAE1B,GAAI,CACF,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGnF,OAAO,QAAQ,CAAE,CAC/CoF,OAAO,CAAE,CACP,eAAe,CAAE,UAAUR,KAAK,EAAE,CAClC,cAAc,CAAE,kBAAkB,CAClC,QAAQ,CAAE,kBACZ,CACF,CAAC,CAAC,CAEF,GAAIM,QAAQ,CAACG,EAAE,CAAE,KAAAC,UAAA,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CAClC,GAAI,CAAAD,IAAI,SAAJA,IAAI,kBAAAD,UAAA,CAAJC,IAAI,CAAEA,IAAI,UAAAD,UAAA,iBAAVA,UAAA,CAAYG,MAAM,EAAG,CAAC,EAAKC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAIA,IAAI,CAACE,MAAM,CAAG,CAAE,CAAE,CACtER,UAAU,CAAG,CAAAM,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEA,IAAI,GAAIA,IAAI,CACjC,CACF,CACF,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAEF,KAAK,CAAC,CACnE,CAEArB,QAAQ,CAACU,UAAU,CAACc,GAAG,CAAC,CAACpE,IAAI,CAAEC,GAAG,GAAKF,aAAa,CAACC,IAAI,CAAEC,GAAG,CAAC,CAAC,CAAC,CACnE,CAAE,MAAOgE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CjB,MAAM,CAAC,+CAA+C,CAAC,CACzD,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CACDM,IAAI,CAAC,CAAC,CACR,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIP,OAAO,CAAE,mBAAOrE,IAAA,CAACF,OAAO,GAAE,CAAC,CAC/B,GAAIyE,GAAG,CAAE,mBAAOvE,IAAA,QAAKQ,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAE8D,GAAG,CAAM,CAAC,CACzD,GAAI,CAACJ,KAAK,CAACmB,MAAM,CAAE,MAAO,KAAI,CAE9B,mBACEtF,IAAA,QAAKQ,SAAS,CAAC,qFAAqF,CAAAC,QAAA,CACjG0D,KAAK,CAACyB,GAAG,CAAErF,IAAI,eACdP,IAAA,CAACG,QAAQ,EAAeI,IAAI,CAAEA,IAAK,EAApBA,IAAI,CAACkC,EAAiB,CACtC,CAAC,CACC,CAAC,CAEV,CAEA;AACA,cAAe,CAAAyB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}