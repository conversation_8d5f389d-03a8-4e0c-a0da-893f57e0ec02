<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdatePasswordManagerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization handled in controller
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        // Get the password manager ID from the route parameter
        $passwordManager = $this->route('password_manager');
        $passwordManagerId = $passwordManager ? $passwordManager->id : null;
        $userId = $passwordManager ? $passwordManager->user_id : ($this->input('user_id') ?? auth()->id());

        return [
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
            'user_id' => 'nullable|exists:users,id',
            'password_title' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                // Removed unique constraint so any user can use the same title multiple times
            ],
            'username' => 'sometimes|required|string|max:255',
            'user_name' => 'sometimes|string|max:255|nullable', // Backward compatibility
            'password' => 'sometimes|string|min:1', // FIXED: Removed max length limit
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // FIXED: Better backward compatibility handling for updates
        $passwordTitle = $this->input('password_title');
        $username = $this->input('username');
        $userNameValue = $this->input('user_name');

        // If user_name is provided but password_title is not, use user_name for password_title
        if (!$passwordTitle && $userNameValue) {
            $this->merge(['password_title' => $userNameValue]);
        }

        // If username is not provided but user_name is, use user_name for username
        if (!$username && $userNameValue) {
            $this->merge(['username' => $userNameValue]);
        }

        // Set user_name for backward compatibility
        if ($passwordTitle || $userNameValue) {
            $this->merge([
                'user_name' => $passwordTitle ?: $userNameValue
            ]);
        }
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'department_id.exists' => 'The selected department does not exist.',
            'team_id.exists' => 'The selected team does not exist.',
            'user_id.exists' => 'The selected user does not exist.',
            'password_title.required' => 'Platform/Service title is required.',
            'password_title.max' => 'Platform/Service title may not be greater than 255 characters.',
            'password_title.unique' => 'This platform/service title already exists for this user.',
            'password.min' => 'Password must be at least 1 character.',
            'password.max' => 'Password may not be greater than 1000 characters.',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param Validator $validator
     * @return void
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422)
        );
    }
}