import { useState, useEffect } from 'react';
const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token && token.length > 0;
};

export const useTeamData = () => {
  const [teamLead, setTeamLead] = useState('');
  const [totalMembers, setTotalMembers] = useState(0);
  const [billableHours, setBillableHours] = useState(0);

  useEffect(() => {
    const fetchTeamData = async () => {
      if (!isTokenValid()) return;
      
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(
          `${process.env.REACT_APP_BASE_API_URL}team/summary`,
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );
        
        if (!response.ok) throw new Error('Failed to fetch team data');
        
        const data = await response.json();
        setTeamLead(data.teamLead || 'N/A');
        setTotalMembers(data.totalMembers || 0);
        setBillableHours(data.billableHours || 0);
      } catch (error) {
        console.error('Error fetching team data:', error);
      }
    };

    fetchTeamData();
  }, []);

  return { teamLead, totalMembers, billableHours };
};