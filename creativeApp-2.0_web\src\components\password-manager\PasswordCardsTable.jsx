// src/components/pages/passwordManager/PasswordCardsTable.js
import React, { useState, useEffect } from "react";
import AddPasswordCardForm from "./AddPasswordCardForm";
import { confirmationAlert } from "../../common/coreui";
import FetchLoggedInRole from "../../common/fetchData/FetchLoggedInRole";
import {
  useGetPasswordManagerDataQuery,
  useDeletePasswordManagerMutation,
  useUpdatePasswordManagerMutation,
} from "../../features/api/passwordManagerApi";
// ADD THESE IMPORTS FOR DROPDOWN DATA
import { useGetDepartmentsWithTeamsQuery } from "../../features/api/departmentApi";
import { useGetTeamsWithDepartmentsQuery } from "../../features/api/teamApi";
import { toast } from "sonner";

const PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {
  // Get current user data
  const { userData } = FetchLoggedInRole();
  const currentUserId = userData?.id;
  // API hooks
  const {
    data: dataItems,
    isLoading,
    refetch,
  } = useGetPasswordManagerDataQuery({
    sort_by: "created_at",
    order: "desc",
    page: 1,
    per_page: 100,
    query: "",
  });
  // ADD THESE API HOOKS FOR DROPDOWN DATA
  const { data: departmentsData } = useGetDepartmentsWithTeamsQuery();
  const { data: teamsData, isLoading: teamsLoading } = useGetTeamsWithDepartmentsQuery();
  console.log('departmentsData:', departmentsData);
  console.log('teamsData:', teamsData);
  const [deletePasswordManager] = useDeletePasswordManagerMutation();
  const [updatePasswordManager] = useUpdatePasswordManagerMutation();

  // State for managing multiple tables
  const [tables, setTables] = useState([{ id: 1, name: "Teams Password Card" }]);

  // State for each table
  const [tableStates, setTableStates] = useState({
    1: {
      showAddForm: false,
      editingRowId: null,
      visiblePasswords: {},
      shareableCards: [],
      editingHeader: false,
      headerTitle: "Teams Password Card",
    },
  });

  // State to track which passwords belong to which table
  const [tablePasswordAssociations, setTablePasswordAssociations] = useState({});

  // FIXED: Load persisted tables and associations from localStorage on mount
  useEffect(() => {
    if (!currentUserId) return;
    const storageKey = `passwordTables_${currentUserId}`;
    const persistedData = localStorage.getItem(storageKey);
    if (persistedData) {
      const { tables: persistedTables, associations: persistedAssociations } = JSON.parse(persistedData);
      setTables(persistedTables);
      setTablePasswordAssociations(persistedAssociations);
      // Initialize tableStates for loaded tables
      const newTableStates = {};
      persistedTables.forEach(table => {
        newTableStates[table.id] = tableStates[table.id] || {
          showAddForm: false,
          editingRowId: null,
          visiblePasswords: {},
          shareableCards: [],
          editingHeader: false,
          headerTitle: table.name,
        };
      });
      setTableStates(newTableStates);
    }
  }, [currentUserId]);

  // FIXED: Save tables and associations to localStorage whenever they change
  useEffect(() => {
    if (!currentUserId) return;
    const storageKey = `passwordTables_${currentUserId}`;
    localStorage.setItem(storageKey, JSON.stringify({
      tables,
      associations: tablePasswordAssociations,
    }));
  }, [tables, tablePasswordAssociations, currentUserId]);

  // Initialize existing passwords ONLY if no persisted data
  const [initializedExistingPasswords, setInitializedExistingPasswords] = useState(false);
  useEffect(() => {
    if (
      !initializedExistingPasswords &&
      dataItems?.data &&
      dataItems.data.length > 0 &&
      Object.keys(tablePasswordAssociations).length === 0 // Check if no persisted associations
    ) {
      const existingPasswordIds = dataItems.data.map((item) => item.id);
      setTablePasswordAssociations({ 1: existingPasswordIds });
      setInitializedExistingPasswords(true);
    }
  }, [dataItems, initializedExistingPasswords, tablePasswordAssociations]);

  // --- NEW FUNCTION: Add a new empty table ---
  const handleAddNewTable = () => {
    // Find the next available ID for the new table
    const newTableId = tables.length > 0 ? Math.max(...tables.map(t => t.id)) + 1 : 1;
    const newTableName = `Teams Password Card ${newTableId}`; // Or any default name

    // Add the new table to the tables list
    setTables((prev) => [...prev, { id: newTableId, name: newTableName }]);

    // Initialize the state for the new table
    setTableStates((prev) => ({
      ...prev,
      [newTableId]: {
        showAddForm: false,
        editingRowId: null,
        visiblePasswords: {},
        shareableCards: [],
        editingHeader: false,
        headerTitle: newTableName,
      },
    }));

    // Initialize empty password association for the new table
    setTablePasswordAssociations((prev) => ({
      ...prev,
      [newTableId]: [],
    }));

    toast.success(`New table "${newTableName}" created!`);
  };

  // Get state for specific table
  const getTableState = (tableId) => {
    return (
      tableStates[tableId] || {
        showAddForm: false,
        editingRowId: null,
        visiblePasswords: {},
        shareableCards: [],
        editingHeader: false,
        headerTitle: `Teams Password Card ${tableId}`,
      }
    );
  };

  // Update state for specific table
  const updateTableState = (tableId, updates) => {
    setTableStates((prev) => ({
      ...prev,
      [tableId]: {
        ...getTableState(tableId),
        ...updates,
      },
    }));
  };

  // Get passwords for a specific table
  const getTableData = (tableId) => {
    const allData = dataItems?.data || [];
    const tablePasswords = tablePasswordAssociations[tableId] || [];
    return allData.filter((item) => tablePasswords.includes(item.id));
  };

  // Associate a password with a table
  const associatePasswordWithTable = (tableId, passwordId) => {
    setTablePasswordAssociations((prev) => ({
      ...prev,
      [tableId]: [...(prev[tableId] || []), passwordId],
    }));
  };

  // Remove password association from table
  const removePasswordFromTable = (tableId, passwordId) => {
    setTablePasswordAssociations((prev) => ({
      ...prev,
      [tableId]: (prev[tableId] || []).filter((id) => id !== passwordId),
    }));
  };

  // Get team members for avatar display - FIXED: Proper backend integration
  const getTeamMembers = () => {
    const members = [];
    // Add current user first
    if (userData) {
      members.push({
        id: userData.id,
        fname: userData.fname || "User",
        lname: userData.lname || "",
        photo: userData.photo || null,
      });
    }
    // FIXED: Add team members from backend data with proper structure
    if (userData?.teams && userData.teams.length > 0) {
      userData.teams.forEach((team) => {
        // Check if team has users (members) loaded
        if (team.users && Array.isArray(team.users)) {
          team.users.forEach((member) => {
            // Avoid duplicate current user
            if (member.id !== userData.id) {
              members.push({
                id: member.id,
                fname: member.fname || "Team",
                lname: member.lname || "Member",
                photo: member.photo || null,
              });
            }
          });
        }
      });
    }
    // If no team members found, add some placeholder members for demo
    if (members.length === 1) {
      for (let i = 2; i <= 4; i++) {
        members.push({
          id: `placeholder_${i}`,
          fname: `Team`,
          lname: `Member ${i}`,
          photo: null,
        });
      }
    }
    return members.slice(0, 4); // Show max 4 avatars
  };

  // Toggle password visibility for specific table
  const togglePasswordVisibility = (tableId, cardId) => {
    const currentState = getTableState(tableId);
    updateTableState(tableId, {
      visiblePasswords: {
        ...currentState.visiblePasswords,
        [cardId]: !currentState.visiblePasswords[cardId],
      },
    });
  };

  // Handle edit for specific table
  const handleEdit = (tableId, cardId) => {
    const currentState = getTableState(tableId);
    if (currentState.editingRowId === cardId) {
      // Exit edit mode
      updateTableState(tableId, { editingRowId: null });
    } else {
      // Enter edit mode
      updateTableState(tableId, { editingRowId: cardId });
    }
  };

  // UPDATED: Handle inline edit save with proper dropdown support
  const handleInlineEditSave = async (tableId, cardId, field, value) => {
    try {
      const item = dataItems?.data?.find((item) => item.id === cardId);
      if (!item) {
        toast.error("Item not found");
        return;
      }
      // UPDATED: Proper data structure for backend with dropdown support
      let updateData = {
        password_title: item.password_title || item.title,
        username: item.username,
        password: item.password,
        department_id: item.department_id,
        team_id: item.team_id,
        user_id: item.user_id || currentUserId, // Always include user_id for backend validation
      };
      // UPDATED: Handle different field updates including dropdown selections
      if (field === "title") {
        updateData.password_title = value;
      } else if (field === "username") {
        updateData.username = value;
      } else if (field === "password") { // Handle password update
         updateData.password = value;
      } else if (field === "team") {
        // Handle team dropdown selection - value should be team ID
        updateData.team_id = value ? parseInt(value) : null;
      } else if (field === "department") {
        // Handle department dropdown selection - value should be department ID
        updateData.department_id = value ? parseInt(value) : null;
        // Reset team when department changes
        updateData.team_id = null;
      }
      console.log("Updating with data:", updateData); // Debug log
      await updatePasswordManager({ id: cardId, ...updateData }).unwrap();
      // FIXED: Await refetch to ensure data is refreshed before proceeding
      await refetch();
      // FIXED: Auto-exit editing mode after successful save
      updateTableState(tableId, { editingRowId: null });
      toast.success("Successfully updated!");
    } catch (error) {
      console.error("Error updating:", error);
      // Improved error handling for duplicate title
      if (error?.data?.errors?.password_title?.[0]) {
        toast.error(error.data.errors.password_title[0]);
      } else {
        toast.error(
          error?.data?.message || "Failed to update. Please try again."
        );
      }
    }
  };

  // Handle delete
  const handleDelete = async (cardId) => {
    confirmationAlert({
      onConfirm: async () => {
        try {
          await deletePasswordManager(cardId).unwrap();
          // Remove from all table associations
          Object.keys(tablePasswordAssociations).forEach((tableId) => {
            removePasswordFromTable(parseInt(tableId), cardId);
          });
          refetch();
          toast.success("Password deleted successfully!");
        } catch (error) {
          console.error("Error deleting password:", error);
          toast.error("Failed to delete password. Please try again.");
        }
      },
    });
  };

  // Handle successful password creation/update - FIXED: Reset form properly
  const handlePasswordSuccess = (tableId, newPasswordId = null) => {
    refetch().then(() => {
      // If a new password was created, associate it with the table
      if (newPasswordId) {
        associatePasswordWithTable(tableId, newPasswordId);
      }
    });
    updateTableState(tableId, {
      showAddForm: false,
      editingRowId: null,
    });
    // FIXED: Only one success notification, no duplicate
    toast.success("Password saved successfully!");
  };

  // Toggle shareable card selection
  const toggleShareableCard = (tableId, cardId) => {
    const currentState = getTableState(tableId);
    const currentShareable = currentState.shareableCards;
    const newShareable = currentShareable.includes(cardId)
      ? currentShareable.filter((id) => id !== cardId)
      : [...currentShareable, cardId];
    updateTableState(tableId, { shareableCards: newShareable });
  };

  // Toggle all shareable cards
  const toggleAllShareable = (tableId) => {
    const currentState = getTableState(tableId);
    const tableData = dataItems?.data || [];
    if (currentState.shareableCards.length === tableData.length) {
      updateTableState(tableId, { shareableCards: [] });
    } else {
      updateTableState(tableId, {
        shareableCards: tableData.map((card) => card.id),
      });
    }
  };

  // FIXED: Handle share - Now copies selected rows to clipboard
  const handleShare = async (tableId) => {
    const currentState = getTableState(tableId);
    const tableData = getTableData(tableId);
    const selectedCards = tableData.filter((card) =>
      currentState.shareableCards.includes(card.id)
    );

    if (selectedCards.length === 0) {
      toast.error("Please select at least one row to copy.");
      return;
    }

    // Format the selected rows as a string (e.g., "Title: title, Username: username, Password: password")
    const copiedText = selectedCards
      .map(
        (card) =>
          `Title: ${card.password_title || card.title}, ` +
          `Username: ${card.username}, ` +
          `Password: ${card.password}`
      )
      .join("\n");

    // Copy to clipboard
    try {
      await navigator.clipboard.writeText(copiedText);
      toast.success(`Successfully copied ${selectedCards.length} row(s) to clipboard!`);
      // Clear selections after copy
      updateTableState(tableId, { shareableCards: [] });
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      toast.error("Failed to copy rows. Please try again.");
    }
  };

  // --- UPDATED FUNCTION: Handle delete entire table entries or selected entries ---
  const handleDeleteTableEntries = async (tableId) => {
    const currentState = getTableState(tableId);
    const tableData = getTableData(tableId); // Gets passwords associated with this table

    // FIXED: Special handling for empty tables - Delete the entire table directly
    if (tableData.length === 0) {
      if (tables.length <= 1) {
        toast.error("Cannot delete the last table. At least one table must remain.");
        return;
      }

      confirmationAlert({
        title: "Delete Empty Table?",
        text: `Are you sure you want to delete the empty table "${getTableState(tableId).headerTitle}"? This action cannot be undone.`,
        onConfirm: async () => {
          try {
            // Remove the table from state and associations (no backend deletions needed since empty)
            setTables((prev) => prev.filter((table) => table.id !== tableId));
            setTableStates((prev) => {
              const newState = { ...prev };
              delete newState[tableId];
              return newState;
            });
            setTablePasswordAssociations((prev) => {
              const newState = { ...prev };
              delete newState[tableId];
              return newState;
            });

            // Refetch (though not strictly needed for empty table)
            await refetch();

            toast.success(`Empty table "${getTableState(tableId)?.headerTitle || 'Unknown'}" deleted successfully.`);
          } catch (error) {
            console.error("Error deleting empty table:", error);
            toast.error("Failed to delete table. Please try again.");
          }
        },
      });
      return;
    }

    // For non-empty tables: Existing logic
    if (currentState.shareableCards.length === 0) {
      toast.error("Please select at least one row to delete.");
      return;
    }

    const userOwnedCards = currentState.shareableCards.filter((cardId) => {
      const card = tableData.find((c) => c.id === cardId);
      return card && card.user_id === currentUserId;
    });

    if (userOwnedCards.length === 0) {
      toast.error("You can only delete password entries that you created.");
      return;
    }

    // FIXED: Special logic for secondary tables (id > 1)
    const isSecondaryTable = tableId > 1;
    const allEntriesSelected = tableData.length > 0 && currentState.shareableCards.length === tableData.length;

    if (isSecondaryTable || allEntriesSelected) {
      // For secondary tables (any selection) or primary table (all selected): Delete entire table
      if (tables.length <= 1) {
        // Prevent deletion if it's the last table
        // Clear associations instead
        setTablePasswordAssociations(prev => ({
          ...prev,
          [tableId]: [] // Clear password associations for this table
        }));
        updateTableState(tableId, { shareableCards: [] }); // Clear selection
        await refetch();
        toast.success(`All entries cleared from the last table.`);
        return;
      }

      confirmationAlert({
        title: "Delete Entire Table?",
        text: `Are you sure you want to delete the entire table "${getTableState(tableId).headerTitle}" and all its entries? This action cannot be undone.`,
        onConfirm: async () => {
          try {
            // 1. Delete user-owned password entries from backend
            const tableData = getTableData(tableId);
            const userOwnedCardIds = tableData
              .filter(card => card.user_id === currentUserId)
              .map(card => card.id);

            if (userOwnedCardIds.length > 0) {
              await Promise.all(
                userOwnedCardIds.map((cardId) =>
                  deletePasswordManager(cardId).unwrap()
                )
              );
            }

            // 2. Remove the table from state and associations
            setTables((prev) => prev.filter((table) => table.id !== tableId));
            setTableStates((prev) => {
              const newState = { ...prev };
              delete newState[tableId];
              return newState;
            });
            setTablePasswordAssociations((prev) => {
              const newState = { ...prev };
              delete newState[tableId];
              return newState;
            });

            // 3. Refetch data
            await refetch();

            toast.success(`Table "${getTableState(tableId)?.headerTitle || 'Unknown'}" and its entries deleted successfully.`);
          } catch (error) {
            console.error("Error deleting table or entries:", error);
            toast.error("Failed to delete table or some entries. Please try again.");
          }
        },
      });
      return;
    }

    // For primary table (partial selection): Delete only selected entries
    confirmationAlert({
      onConfirm: async () => {
        try {
          await Promise.all(
            userOwnedCards.map((cardId) =>
              deletePasswordManager(cardId).unwrap()
            )
          );
          // Remove from table associations
          userOwnedCards.forEach((cardId) => {
            removePasswordFromTable(tableId, cardId);
          });
          updateTableState(tableId, { shareableCards: [] });
          await refetch();
          toast.success(
            `Successfully deleted ${userOwnedCards.length} password entries from this table.`
          );
        } catch (error) {
          console.error("Error deleting password entries:", error);
          toast.error(
            "Failed to delete some password entries. Please try again."
          );
        }
      },
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-900 px-4 sm:px-6 lg:px-8">
      {/* Render all tables */}
      {tables.map((table) => {
        const tableState = getTableState(table.id);
        const teamMembers = getTeamMembers();
        const tableData = getTableData(table.id);
        return (
          <div key={table.id} className="mb-6 sm:mb-8">
            {/* RESPONSIVE Table Header */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6 gap-4 sm:gap-0">
              <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
                {/* Editable Table Title */}
                <div className="flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto">
                  {tableState.editingHeader ? (
                    <input
                      type="text"
                      value={tableState.headerTitle}
                      onChange={(e) =>
                        updateTableState(table.id, {
                          headerTitle: e.target.value,
                        })
                      }
                      onBlur={() =>
                        updateTableState(table.id, { editingHeader: false })
                      }
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          updateTableState(table.id, { editingHeader: false });
                        }
                      }}
                      className="w-full sm:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                      autoFocus
                    />
                  ) : (
                    <h2
                      className="text-base sm:text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors"
                      onClick={() =>
                        updateTableState(table.id, { editingHeader: true })
                      }
                      title="Click to edit table name"
                    >
                      {tableState.headerTitle}
                    </h2>
                  )}

                  {/* Action Buttons - Mobile First Layout */}
                  <div className="flex items-center space-x-1 sm:space-x-2">
                    {/* Share Icon */}
                    <button
                      onClick={() => handleShare(table.id)}
                      className="flex items-center justify-center py-1 px-2 sm:px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs sm:text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                      title="Copy selected rows to clipboard"
                    >
                      <span className="material-symbols-outlined text-sm">
                        share
                      </span>
                    </button>
                    {/* Delete Selected Icon */}
                    <button
                      onClick={() => handleDeleteTableEntries(table.id)}
                      className="flex items-center justify-center py-1 px-2 sm:px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-xs sm:text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                      title="Delete selected entries or table"
                    >
                      <span className="material-symbols-outlined text-sm">
                        delete
                      </span>
                    </button>
                  </div>
                </div>

                {/* Team Member Avatars - Responsive */}
                <div className="flex items-center -space-x-2 ml-0 sm:ml-2">
                  {teamMembers.slice(0, 3).map((member, index) => (
                    <div key={index} className="relative">
                      {member?.photo ? (
                        <img
                          src={
                            member?.photo
                              ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${member.photo}`
                              : "/default-avatar.png"
                          }
                          alt={`${member.fname || "User"} ${
                            member.lname || ""
                          }`}
                          className="w-6 h-6 sm:w-8 sm:h-8 rounded-full object-cover border-2 border-white shadow-sm"
                          onError={(e) => {
                            e.target.style.display = "none";
                            e.target.nextSibling.style.display = "flex";
                          }}
                        />
                      ) : null}
                      {/* Fallback avatar */}
                      <div
                        className="w-6 h-6 sm:w-8 sm:h-8 bg-primary rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white shadow-sm"
                        style={{ display: member?.photo ? "none" : "flex" }}
                      >
                        {member?.fname?.charAt(0) || "T"}
                        {member?.lname?.charAt(0) || "M"}
                      </div>
                    </div>
                  ))}
                  {teamMembers.length > 3 && (
                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 text-xs font-medium border-2 border-white shadow-sm">
                      +{teamMembers.length - 3}
                    </div>
                  )}
                </div>
              </div>

              {/* Add Password Button - Responsive */}
              <button
                onClick={() => {
                  updateTableState(table.id, {
                    editingRowId: null,
                    showAddForm: !tableState.showAddForm,
                  });
                }}
                className="flex items-center justify-center py-2 px-3 sm:px-4 text-xs sm:text-sm font-medium bg-transparent text-black border-2 border-[#0B333F] rounded-full hover:bg-primary hover:text-white transition duration-500 ease-in-out focus:outline-none focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 shadow-sm w-full sm:w-auto sm:min-w-[200px] h-[36px] sm:h-[40px]"
              >
                <svg
                  className="w-4 h-4 sm:w-6 sm:h-6 mr-1 sm:mr-2 -ml-2 sm:-ml-3 flex items-center justify-center rounded-full bg-white border-2 border-black"
                  fill="none"
                  stroke="black"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Add Password
              </button>
            </div>

            {/* Add Password Form */}
            {tableState.showAddForm && (
              <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-gray-50 rounded-lg border border-gray-200">
                <AddPasswordCardForm
                  onCancel={() => {
                    updateTableState(table.id, {
                      showAddForm: false,
                      editingRowId: null,
                    });
                  }}
                  onSuccess={(newPasswordId) =>
                    handlePasswordSuccess(table.id, newPasswordId)
                  }
                  generatedPassword={generatedPassword}
                  passwordStrength={passwordStrength}
                  editData={
                    tableState.editingRowId
                      ? tableData.find((item) => item.id === tableState.editingRowId)
                      : null
                  }
                  departmentsData={departmentsData}
                  teamsData={teamsData}
                />
              </div>
            )}

            {/* RESPONSIVE Table Container */}
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                {tableData.length === 0 ? (
                  <div className="text-center py-8 sm:py-12 px-4">
                    <p className="text-gray-500 mb-4 text-sm sm:text-base">
                      No password cards added yet. Click "Add Password" to
                      add your first card.
                    </p>
                  </div>
                ) : (
                  <table className="w-full text-xs sm:text-sm text-left min-w-[800px]">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th scope="col" className="px-2 sm:px-4 py-2 sm:py-3 w-8 sm:w-12">
                          <input
                            type="checkbox"
                            checked={
                              tableState.shareableCards.length ===
                                tableData.length && tableData.length > 0
                            }
                            onChange={() => toggleAllShareable(table.id)}
                            className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                          />
                        </th>
                        <th
                          scope="col"
                          className="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] sm:min-w-[150px]"
                        >
                          <div className="flex items-center">
                            Title
                            <svg
                              className="w-3 h-3 ml-1 text-gray-400"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] sm:min-w-[150px]"
                        >
                          <div className="flex items-center">
                            User Name
                            <svg
                              className="w-3 h-3 ml-1 text-gray-400"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] sm:min-w-[150px]"
                        >
                          Password
                        </th>
                        <th
                          scope="col"
                          className="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px] sm:min-w-[120px]"
                        >
                          Team
                        </th>
                        <th
                          scope="col"
                          className="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px] sm:min-w-[120px]"
                        >
                          Department
                        </th>
                        <th
                          scope="col"
                          className="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px] sm:min-w-[100px]"
                        >
                          Level
                        </th>
                        <th
                          scope="col"
                          className="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px] sm:min-w-[100px]"
                        >
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {tableData.map((card) => (
                        <TableRow
                          key={card.id}
                          card={card}
                          tableId={table.id}
                          isEditing={tableState.editingRowId === card.id}
                          isShareable={tableState.shareableCards.includes(
                            card.id
                          )}
                          visiblePassword={tableState.visiblePasswords[card.id]}
                          currentUserId={currentUserId}
                          // PASS CORRECT DROPDOWN DATA TO TableRow
                          departmentsData={departmentsData}
                          teamsData={teamsData}
                          onEdit={handleEdit}
                          onDelete={handleDelete}
                          onTogglePassword={togglePasswordVisibility}
                          onToggleShareable={toggleShareableCard}
                          onInlineEditSave={handleInlineEditSave}
                        />
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
            
            {/* RESPONSIVE Add New Password Card Button */}
            <div className="flex justify-center mt-4 sm:mt-6">
              <button
                onClick={handleAddNewTable}
                className="flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 bg-[#006D94] text-white rounded-lg shadow-md hover:bg-[#005F80] transition duration-200 text-sm sm:text-base w-full sm:w-auto max-w-xs sm:max-w-none"
              >
                {/* Circular Plus Icon */}
                <div className="w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center border-2 border-white rounded-full flex-shrink-0">
                  <span className="relative -top-[1px] sm:-top-[2px] text-base sm:text-lg font-thin">
                    +
                  </span>
                </div>
                {/* Button Text */}
                <span className="text-xs sm:text-sm font-medium">
                  Add New Password Card
                </span>
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
};

// UPDATED RESPONSIVE TableRow component
const TableRow = ({
  card,
  tableId,
  isEditing,
  isShareable,
  visiblePassword,
  currentUserId,
  departmentsData = [],
  teamsData = [],
  onEdit,
  onDelete,
  onTogglePassword,
  onToggleShareable,
  onInlineEditSave,
}) => {
  // --- UPDATED: Proper initialization of edit values including password ---
  const [editValues, setEditValues] = useState({
    title: card.password_title || card.title || card.user_name || "",
    username: card.username || card.user_name || "",
    password: card.password || "",
    team_id: card.team_id ? String(card.team_id) : "",
    department_id: card.department_id ? String(card.department_id) : "",
  });

  // FIXED: Sync editValues with updated card props after refetch
  useEffect(() => {
    setEditValues({
      title: card.password_title || card.title || card.user_name || "",
      username: card.username || card.user_name || "",
      password: card.password || "",
      team_id: card.team_id ? String(card.team_id) : "",
      department_id: card.department_id ? String(card.department_id) : "",
    });
  }, [card]);

  // --- UPDATED: Handle input changes with department/team relationship logic and password ---
  const handleInputChange = (field, value) => {
    if (field === "department_id") {
      setEditValues((prev) => ({
        ...prev,
        [field]: value,
        team_id: "",
      }));
    } else {
      setEditValues((prev) => ({ ...prev, [field]: value }));
    }
  };

  // --- UPDATED: Proper field value comparison and update for all fields including password ---
  const handleInputBlur = (field) => {
    let originalValue;
    if (field === "title") {
      originalValue = card.password_title || card.title || card.user_name;
    } else if (field === "username") {
      originalValue = card.username || card.user_name;
    } else if (field === "password") {
       originalValue = card.password;
    } else if (field === "team_id") {
      originalValue = card.team_id ? String(card.team_id) : "";
    } else if (field === "department_id") {
      originalValue = card.department_id ? String(card.department_id) : "";
    }
    // Client-side validation for required fields
    const requiredFields = ["title", "username", "password", "department_id", "team_id"];
    if (requiredFields.includes(field) && !editValues[field]) {
      toast.error(`The ${field.replace('_', ' ')} field is required.`);
      return;
    }
    if (editValues[field] !== originalValue) {
      const backendField =
        field === "team_id"
          ? "team"
          : field === "department_id"
          ? "department"
          : field;
      onInlineEditSave(tableId, card.id, backendField, editValues[field]);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      e.target.blur();
    }
  };

  // Get password strength styling
  const getPasswordStrengthStyle = (level) => {
    switch (level) {
      case "Strong":
        return "bg-green/10 text-[#22C55E]";
      case "Moderate":
        return "bg-yellow/10 text-[#F59E0B]";
      case "Weak":
        return "bg-red/10 text-[#EF4444]";
      default:
        return "bg-green-100 text-green-700 border border-green-200";
    }
  };

  // UPDATED: Get filtered teams based on selected department
  const getFilteredTeams = () => {
    if (!editValues.department_id || !Array.isArray(teamsData)) return [];
    return teamsData.filter(
      (team) =>
        team.department_ids &&
        team.department_ids.includes(parseInt(editValues.department_id))
    );
  };

  return (
    <tr className="hover:bg-gray-50 transition-colors">
      {/* Checkbox Column */}
      <td className="px-2 sm:px-4 py-3 sm:py-4">
        <input
          type="checkbox"
          checked={isShareable}
          onChange={() => onToggleShareable(tableId, card.id)}
          className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
        />
      </td>
      
      {/* Title Column */}
      <td className="px-2 sm:px-4 py-3 sm:py-4">
        <div className="flex items-center">
          {/* Platform Icon Circle - Responsive */}
          <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-2 sm:mr-3 text-xs sm:text-sm flex-shrink-0">
            {(card.password_title || card.title || card.user_name)
              ?.charAt(0)
              ?.toUpperCase() || "P"}
          </div>
          {isEditing ? (
            <input
              type="text"
              value={editValues.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              onBlur={() => handleInputBlur("title")}
              onKeyDown={handleKeyDown}
              className="font-medium text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-primary focus:border-blue-500 text-xs sm:text-sm"
              autoFocus
              placeholder="Platform Title"
            />
          ) : (
            <span className="font-medium text-gray-900 text-xs sm:text-sm break-words">
              {card.password_title || card.title || card.user_name}
            </span>
          )}
        </div>
      </td>
      
      {/* User Name Column */}
      <td className="px-2 sm:px-4 py-3 sm:py-4">
        <div className="flex items-center">
          {isEditing ? (
            <input
              type="text"
              value={editValues.username}
              onChange={(e) => handleInputChange("username", e.target.value)}
              onBlur={() => handleInputBlur("username")}
              onKeyDown={handleKeyDown}
              className="text-gray-900 border border-gray-300 rounded px-2 py-1 w-full mr-1 sm:mr-2 focus:ring-2 focus:ring-primary focus:border-blue-500 text-xs sm:text-sm"
              placeholder="Username or Email"
            />
          ) : (
            <span className="text-gray-900 mr-1 sm:mr-2 text-xs sm:text-sm break-all">
              {card.username || card.user_name}
            </span>
          )}
          {/* Copy Username Icon - Responsive */}
          <button
            className="flex items-center justify-center py-1 px-1 sm:px-2 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 flex-shrink-0"
            title="Copy username"
            onClick={() => {
              const username = card.username || card.user_name;
              if (username) {
                navigator.clipboard.writeText(username);
                toast.success("Username copied to clipboard!");
              }
            }}
          >
            <span className="material-symbols-outlined text-sm">
              content_copy
            </span>
          </button>
        </div>
      </td>
      
      {/* Password Column - Responsive */}
      <td className="px-2 sm:px-4 py-3 sm:py-4">
        <div className="flex items-center">
          {isEditing ? (
            <>
              <input
                type={visiblePassword ? "text" : "password"}
                value={editValues.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                onBlur={() => handleInputBlur("password")}
                onKeyDown={handleKeyDown}
                className="text-gray-900 border border-gray-300 rounded px-2 py-1 w-full mr-1 focus:ring-2 focus:ring-primary focus:border-blue-500 font-mono text-xs sm:text-sm"
                placeholder="Password"
              />
              <button
                onClick={() => onTogglePassword(tableId, card.id)}
                className="flex items-center justify-center py-1 px-1 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 mr-1 flex-shrink-0"
                title={visiblePassword ? "Hide password" : "Show password"}
                type="button"
              >
                <span className="material-symbols-outlined text-sm">
                  {visiblePassword ? "visibility_off" : "visibility"}
                </span>
              </button>
            </>
          ) : (
            <>
              <span className="text-gray-900 mr-1 font-mono text-xs sm:text-sm break-all">
                {visiblePassword ? card.password : "••••••••••••"}
              </span>
              <button
                onClick={() => onTogglePassword(tableId, card.id)}
                className="flex items-center justify-center py-1 px-1 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 mr-1 flex-shrink-0"
                title={visiblePassword ? "Hide password" : "Show password"}
                type="button"
              >
                <span className="material-symbols-outlined text-sm">
                  {visiblePassword ? "visibility_off" : "visibility"}
                </span>
              </button>
            </>
          )}
          {/* Copy Password Icon - Responsive */}
          <button
            className="flex items-center justify-center py-1 px-1 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 flex-shrink-0"
            title="Copy password"
            onClick={() => {
              const passwordToCopy = isEditing
                ? editValues.password
                : card.password;
              if (passwordToCopy) {
                navigator.clipboard.writeText(passwordToCopy);
                toast.success("Password copied to clipboard!");
              }
            }}
            type="button"
          >
            <span className="material-symbols-outlined text-sm">
              content_copy
            </span>
          </button>
        </div>
      </td>
      
      {/* Team Column - Responsive Dropdown */}
      <td className="px-2 sm:px-4 py-3 sm:py-4">
        {isEditing ? (
          <select
            value={editValues.team_id}
            onChange={(e) => handleInputChange("team_id", e.target.value)}
            onBlur={() => handleInputBlur("team_id")}
            disabled={!editValues.department_id}
            className={`text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs sm:text-sm ${
              !editValues.department_id ? "bg-gray-100 cursor-not-allowed" : ""
            }`}
          >
            <option value="">Select Team</option>
            {getFilteredTeams().map((team) => (
              <option key={team.id} value={team.id}>
                {team.name}
              </option>
            ))}
          </select>
        ) : (
          <span className="text-gray-900 text-xs sm:text-sm break-words">
            {card.team?.name || "Team Name"}
          </span>
        )}
      </td>
      
      {/* Department Column - Responsive Dropdown */}
      <td className="px-2 sm:px-4 py-3 sm:py-4">
        {isEditing ? (
          <select
            value={editValues.department_id || ""}
            onChange={(e) => handleInputChange("department_id", e.target.value)}
            onBlur={() => handleInputBlur("department_id")}
            className="text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs sm:text-sm"
          >
            <option value="">Select Department</option>
            {departmentsData.map((dept) => (
              <option key={dept.id} value={dept.id}>
                {dept.name}
              </option>
            ))}
          </select>
        ) : (
          <span className="text-gray-900 text-xs sm:text-sm break-words">
            {card.department?.name || "Department name"}
          </span>
        )}
      </td>
      
      {/* Level Column - Responsive */}
      <td className="px-2 sm:px-4 py-3 sm:py-4">
        <span
          className={`px-2 py-1 text-xs font-medium rounded ${getPasswordStrengthStyle(
            card.level
          )}`}
        >
          {card.level || "Strong Password"}
        </span>
      </td>
      
      {/* Action Column - Responsive */}
      <td className="px-2 sm:px-4 py-3 sm:py-4">
        <div className="flex items-center space-x-1">
          {/* Edit Button */}
          <button
            onClick={() => onEdit(tableId, card.id)}
            className="flex items-center justify-center py-1 px-1 sm:px-2 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200"
            title="Edit"
          >
            <span className="material-symbols-outlined text-sm">
              stylus_note
            </span>
          </button>
          {/* Delete Icon - Only for card owner */}
          {card.user_id === currentUserId && (
            <button
              onClick={() => onDelete(card.id)}
              className="flex items-center justify-center py-1 px-1 sm:px-2 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200"
              title="Delete"
            >
              <span className="material-symbols-outlined text-sm">
                delete
              </span>
            </button>
          )}
        </div>
      </td>
    </tr>
  );
};

export default PasswordCardsTable;